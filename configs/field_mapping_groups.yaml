# 字段映射组配置文件
# 基于 docs/enhanced_system_design_v3.md 中的设计规范
# 支持从Discord Embed字段到ProductItem字段的映射和转换

field_mapping_groups:
  # Amazon标准映射组
  amazon_standard:
    name: "Amazon标准映射"
    platform: "amazon"
    description: "适用于Amazon产品信息的标准字段映射"
    
    # 字段映射规则
    mappings:
      - source_field: "ASIN"           # 从Embed字段提取
        target_field: "ProductID"
        required: true
        description: "Amazon产品唯一标识符"

      - source_field: "Title"          # 直接映射Embed标题
        target_field: "Title"
        fallback_fields: ["Name", "Product Name"]
        description: "产品标题"

      - source_field: "PRICE"
        target_field: "Price"
        transform: "parse_price"
        description: "产品价格，支持多种货币格式"

      - source_field: "embed_url"
        target_field: "URL"
        description: "产品链接"

      - source_field: "Stock"
        target_field: "StockInfo"
        fallback_fields: ["Availability", "库存"]
        description: "库存信息"

      - source_field: "Image"
        target_field: "ImageURL"
        fallback_fields: ["image_url", "thumbnail"]
        description: "产品图片链接"

    # 默认值
    defaults:
      Platform: "amazon"
      Country: "US"
      Availability: "unknown"
      Stock: 0

    # 数据转换规则
    transforms:
      parse_price:
        type: "regex"
        pattern: "\\$?([0-9,]+\\.?[0-9]*)"
        description: "提取价格数字部分"

  # eBay标准映射组
  ebay_standard:
    name: "eBay标准映射"
    platform: "ebay"
    description: "适用于eBay产品信息的标准字段映射"
    
    mappings:
      - source_field: "Item ID"
        target_field: "ProductID"
        required: true
        description: "eBay商品ID"

      - source_field: "Title"
        target_field: "Title"
        fallback_fields: ["Item Title"]
        description: "商品标题"

      - source_field: "Price"
        target_field: "Price"
        transform: "parse_price"
        description: "商品价格"

      - source_field: "Buy It Now"
        target_field: "AtcLink"
        description: "立即购买链接"

      - source_field: "Seller"
        target_field: "Platform"
        transform: "add_ebay_prefix"
        description: "卖家信息"

    defaults:
      Platform: "ebay"
      Country: "US"
      Availability: "unknown"
      Stock: 0

    transforms:
      parse_price:
        type: "regex"
        pattern: "\\$?([0-9,]+\\.?[0-9]*)"
        description: "提取价格数字部分"
      add_ebay_prefix:
        type: "template"
        template: "ebay-{value}"
        description: "添加eBay前缀"

  # AliExpress标准映射组
  aliexpress_standard:
    name: "AliExpress标准映射"
    platform: "aliexpress"
    description: "适用于AliExpress产品信息的标准字段映射"
    
    mappings:
      - source_field: "Product ID"
        target_field: "ProductID"
        required: true
        fallback_fields: ["商品ID", "ID"]
        description: "AliExpress商品ID"

      - source_field: "Title"
        target_field: "Title"
        fallback_fields: ["商品标题", "Product Title"]
        description: "商品标题"

      - source_field: "Price"
        target_field: "Price"
        transform: "parse_price_multi_currency"
        fallback_fields: ["价格", "售价"]
        description: "商品价格，支持多种货币"

      - source_field: "Store"
        target_field: "Platform"
        transform: "add_aliexpress_prefix"
        fallback_fields: ["店铺", "商店"]
        description: "店铺信息"

      - source_field: "Shipping"
        target_field: "Addition"
        fallback_fields: ["运费", "配送"]
        description: "运费信息"

    defaults:
      Platform: "aliexpress"
      Country: "CN"
      Availability: "unknown"
      Stock: 0

    transforms:
      parse_price_multi_currency:
        type: "regex"
        pattern: "([¥$€£]?[0-9,]+\\.?[0-9]*)"
        description: "提取多种货币格式的价格"
      add_aliexpress_prefix:
        type: "template"
        template: "aliexpress-{value}"
        description: "添加AliExpress前缀"

  # 通用映射组
  generic:
    name: "通用映射"
    platform: "generic"
    description: "适用于通用产品信息的基础字段映射"
    
    mappings:
      - source_field: "Title"
        target_field: "Title"
        fallback_fields: ["Name", "Product Name", "标题", "名称"]
        description: "产品标题"

      - source_field: "URL"
        target_field: "URL"
        fallback_fields: ["Link", "链接"]
        description: "产品链接"

      - source_field: "Price"
        target_field: "Price"
        transform: "parse_price_multi_currency"
        fallback_fields: ["价格", "售价", "Cost"]
        description: "产品价格"

      - source_field: "ID"
        target_field: "ProductID"
        fallback_fields: ["Product ID", "商品ID", "编号"]
        description: "产品ID"

      - source_field: "Image"
        target_field: "ImageURL"
        fallback_fields: ["Picture", "图片", "Thumbnail"]
        description: "产品图片"

    defaults:
      Platform: "unknown"
      Country: "unknown"
      Availability: "unknown"
      Stock: 0

    transforms:
      parse_price_multi_currency:
        type: "regex"
        pattern: "([¥$€£]?[0-9,]+\\.?[0-9]*)"
        description: "提取多种货币格式的价格"

  # 默认映射组（当没有指定映射组时使用）
  default:
    name: "默认映射"
    platform: "default"
    description: "默认的基础字段映射，当没有指定其他映射组时使用"
    
    mappings:
      - source_field: "Title"
        target_field: "Title"
        description: "产品标题"
      - source_field: "URL"
        target_field: "URL"
        description: "产品链接"
      - source_field: "Price"
        target_field: "Price"
        description: "产品价格"
      - source_field: "ID"
        target_field: "ProductID"
        description: "产品ID"
    
    defaults:
      Platform: "unknown"
      Availability: "unknown"
      Stock: 0

# 全局配置
global_settings:
  # 默认使用的映射组
  default_mapping_group: "default"
  
  # 是否启用自动字段检测
  enable_auto_detection: true
  
  # 字段映射缓存设置
  cache_settings:
    enabled: true
    ttl: "1h"  # 缓存生存时间
    max_size: 1000  # 最大缓存条目数
  
  # 日志设置
  logging:
    enabled: true
    level: "info"
    log_mapping_results: false  # 是否记录映射结果（调试用）
  
  # 错误处理
  error_handling:
    ignore_missing_required_fields: false
    use_fallback_on_transform_error: true
    log_transform_errors: true
