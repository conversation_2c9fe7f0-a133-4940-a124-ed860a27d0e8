package test

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"
)

// TestTaskModule 测试任务模块
type TestTaskModule struct {
	*tasks.BaseTaskModule
}

// NewTestTaskModule 创建测试任务模块
func NewTestTaskModule() *TestTaskModule {
	base := tasks.NewBaseTaskModule(
		"test",
		"处理测试相关任务",
		[]string{"test", "test.simple", "test.complex"},
	)

	module := &TestTaskModule{
		BaseTaskModule: base,
	}

	return module
}

// Initialize 初始化模块
func (m *TestTaskModule) Initialize(client *types.Client, queueService types.QueueService) error {
	logger.Info("🧪 初始化测试任务模块...")

	// 创建并注册任务处理器
	simpleHandler := NewSimpleTestHandler()
	complexHandler := NewComplexTestHandler()
	generalHandler := NewGeneralTestHandler()

	m.RegisterHandler(simpleHandler)
	m.RegisterHandler(complexHandler)
	m.RegisterHandler(generalHandler)

	// 调用基础模块初始化
	if err := m.BaseTaskModule.Initialize(client, queueService); err != nil {
		return fmt.Errorf("基础模块初始化失败: %w", err)
	}

	logger.Info("✅ 测试任务模块初始化完成")
	return nil
}

// Shutdown 关闭模块
func (m *TestTaskModule) Shutdown() error {
	logger.Info("🧪 关闭测试任务模块...")

	// 调用基础模块关闭
	if err := m.BaseTaskModule.Shutdown(); err != nil {
		return fmt.Errorf("基础模块关闭失败: %w", err)
	}

	logger.Info("✅ 测试任务模块已关闭")
	return nil
}

// TestTaskData 测试任务数据结构
type TestTaskData struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`     // simple, complex, general
	Message  string                 `json:"message"`  // 测试消息
	Delay    int                    `json:"delay"`    // 延迟时间（毫秒）
	Payload  map[string]interface{} `json:"payload"`  // 完整载荷
	Metadata map[string]interface{} `json:"metadata"` // 元数据
}

// Validate 验证测试任务数据
func (data *TestTaskData) Validate() error {
	if data.Type == "" {
		data.Type = "general" // 默认类型
	}

	if data.ID == "" {
		// 生成随机ID
		data.ID = generateRandomID()
	}

	if data.Message == "" {
		data.Message = "默认测试消息"
	}

	if data.Delay < 0 {
		data.Delay = 0
	}

	return nil
}

// ParseTestData 解析测试任务数据
func ParseTestData(data interface{}) (*TestTaskData, error) {
	// 尝试直接类型断言
	if taskData, ok := data.(*TestTaskData); ok {
		return taskData, nil
	}

	// 尝试从 map 解析
	if dataMap, ok := data.(map[string]interface{}); ok {
		taskData := &TestTaskData{}

		if id, exists := dataMap["id"]; exists {
			if idStr, ok := id.(string); ok {
				taskData.ID = idStr
			}
		}

		if taskType, exists := dataMap["type"]; exists {
			if typeStr, ok := taskType.(string); ok {
				taskData.Type = typeStr
			}
		}

		if message, exists := dataMap["message"]; exists {
			if messageStr, ok := message.(string); ok {
				taskData.Message = messageStr
			}
		}

		if delay, exists := dataMap["delay"]; exists {
			switch v := delay.(type) {
			case int:
				taskData.Delay = v
			case float64:
				taskData.Delay = int(v)
			}
		}

		if payload, exists := dataMap["payload"]; exists {
			if payloadMap, ok := payload.(map[string]interface{}); ok {
				taskData.Payload = payloadMap
			}
		}

		if metadata, exists := dataMap["metadata"]; exists {
			if metadataMap, ok := metadata.(map[string]interface{}); ok {
				taskData.Metadata = metadataMap
			}
		}

		return taskData, nil
	}

	// 尝试从 JSON 字符串解析
	if jsonStr, ok := data.(string); ok {
		taskData := &TestTaskData{}
		if err := json.Unmarshal([]byte(jsonStr), taskData); err == nil {
			return taskData, nil
		}
	}

	// 如果都失败了，创建一个默认的测试数据
	taskData := &TestTaskData{
		ID:      generateRandomID(),
		Type:    "general",
		Message: fmt.Sprintf("未知数据类型的测试任务: %T", data),
		Delay:   1000,
		Payload: map[string]interface{}{
			"original_data": data,
		},
	}

	return taskData, nil
}

// generateRandomID 生成随机ID
func generateRandomID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳
		return fmt.Sprintf("test_%d", time.Now().UnixNano())
	}
	return "test_" + hex.EncodeToString(bytes)
}

// SimpleTestHandler 简单测试处理器
type SimpleTestHandler struct {
	*tasks.BaseTaskHandler
}

// NewSimpleTestHandler 创建简单测试处理器
func NewSimpleTestHandler() *SimpleTestHandler {
	return &SimpleTestHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"test.simple",
			"处理简单测试任务",
		),
	}
}

// Handle 处理简单测试任务
func (h *SimpleTestHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseTestData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	if err := taskData.Validate(); err != nil {
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("🧪 处理简单测试任务", "id", taskData.ID, "message", taskData.Message)

	// 模拟处理延迟
	if taskData.Delay > 0 {
		time.Sleep(time.Duration(taskData.Delay) * time.Millisecond)
	}

	logger.Info("✅ 简单测试任务处理完成", "id", taskData.ID)
	return nil
}

// ComplexTestHandler 复杂测试处理器
type ComplexTestHandler struct {
	*tasks.BaseTaskHandler
}

// NewComplexTestHandler 创建复杂测试处理器
func NewComplexTestHandler() *ComplexTestHandler {
	return &ComplexTestHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"test.complex",
			"处理复杂测试任务",
		),
	}
}

// Handle 处理复杂测试任务
func (h *ComplexTestHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseTestData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	if err := taskData.Validate(); err != nil {
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("🧪 处理复杂测试任务", "id", taskData.ID, "message", taskData.Message)

	// 记录完整的任务数据
	if taskData.Payload != nil {
		logger.Debug("📝 任务载荷", "payload", taskData.Payload)
	}

	if taskData.Metadata != nil {
		logger.Debug("📋 任务元数据", "metadata", taskData.Metadata)
	}

	// 模拟复杂处理
	steps := []string{"初始化", "数据验证", "业务处理", "结果保存", "清理资源"}
	for i, step := range steps {
		logger.Debug("🔄 执行步骤", "step", i+1, "name", step, "task_id", taskData.ID)
		
		// 模拟每个步骤的处理时间
		stepDelay := taskData.Delay / len(steps)
		if stepDelay > 0 {
			time.Sleep(time.Duration(stepDelay) * time.Millisecond)
		}
	}

	logger.Info("✅ 复杂测试任务处理完成", "id", taskData.ID)
	return nil
}

// GeneralTestHandler 通用测试处理器
type GeneralTestHandler struct {
	*tasks.BaseTaskHandler
}

// NewGeneralTestHandler 创建通用测试处理器
func NewGeneralTestHandler() *GeneralTestHandler {
	return &GeneralTestHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"test",
			"处理通用测试任务",
		),
	}
}

// Handle 处理通用测试任务
func (h *GeneralTestHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseTestData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	logger.Info("🧪 处理通用测试任务", "id", taskData.ID, "type", taskData.Type)

	// 根据类型分发到具体处理器
	switch taskData.Type {
	case "simple":
		simpleHandler := NewSimpleTestHandler()
		return simpleHandler.Handle(ctx, client, data)
	case "complex":
		complexHandler := NewComplexTestHandler()
		return complexHandler.Handle(ctx, client, data)
	default:
		// 默认处理
		if err := taskData.Validate(); err != nil {
			return fmt.Errorf("任务数据验证失败: %w", err)
		}

		logger.Info("📝 任务内容", "message", taskData.Message)

		// 模拟处理延迟
		if taskData.Delay > 0 {
			time.Sleep(time.Duration(taskData.Delay) * time.Millisecond)
		}

		logger.Info("✅ 通用测试任务处理完成", "id", taskData.ID)
		return nil
	}
}
