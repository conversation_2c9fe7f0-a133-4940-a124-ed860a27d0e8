package registry

import (
	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/tasks/instock"
	"zeka-go/internal/tasks/notification"
	"zeka-go/internal/tasks/test"
)

// ModuleRegistry 模块注册器
type ModuleRegistry struct {
	modules []tasks.TaskModule
}

// NewModuleRegistry 创建模块注册器
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		modules: make([]tasks.TaskModule, 0),
	}
}

// RegisterBuiltinModules 注册所有内置模块
func (r *ModuleRegistry) RegisterBuiltinModules() []tasks.TaskModule {
	logger.Info("📦 注册内置任务模块...")

	// 注册所有可用的任务模块
	modules := []tasks.TaskModule{
		instock.NewInStockTaskModule(),
		notification.NewNotificationTaskModule(),
		test.NewTestTaskModule(),
	}

	r.modules = modules
	logger.Info("✅ 内置任务模块注册完成", "count", len(modules))

	return modules
}

// GetModules 获取所有注册的模块
func (r *ModuleRegistry) GetModules() []tasks.TaskModule {
	return r.modules
}

// GetModuleByName 根据名称获取模块
func (r *ModuleRegistry) GetModuleByName(name string) tasks.TaskModule {
	for _, module := range r.modules {
		if module.GetName() == name {
			return module
		}
	}
	return nil
}

// GetModuleNames 获取所有模块名称
func (r *ModuleRegistry) GetModuleNames() []string {
	names := make([]string, len(r.modules))
	for i, module := range r.modules {
		names[i] = module.GetName()
	}
	return names
}
