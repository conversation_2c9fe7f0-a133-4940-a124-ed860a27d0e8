package message_forward

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// MessageForwardTaskHandler 消息转发任务处理器
type MessageForwardTaskHandler struct {
	*tasks.BaseTaskHandler

	// 服务依赖（运行时注入）
	forwardService types.ForwardRuleManager
}

// NewMessageForwardTaskHandler 创建新的消息转发任务处理器
func NewMessageForwardTaskHandler() *MessageForwardTaskHandler {
	return &MessageForwardTaskHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"message_forward",
			"处理频道间的延迟消息转发任务",
		),
	}
}

// SetForwardService 设置转发服务（运行时注入）
func (h *MessageForwardTaskHandler) SetForwardService(forwardService types.ForwardRuleManager) {
	h.forwardService = forwardService
}

// Handle 处理消息转发任务
func (h *MessageForwardTaskHandler) Handle(ctx context.Context, client *types.Client, data any) error {
	startTime := time.Now()

	logger.Info("🚀 开始处理消息转发任务",
		"handler", h.GetType(),
		"forward_service_injected", h.forwardService != nil)

	// 解析任务数据
	task, err := ParseTaskFromData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	// 验证转发规则（优化：静默丢弃无效任务）
	if err := h.validateForwardRules(task); err != nil {
		// 静默丢弃任务，不记录错误日志
		logger.Debug("丢弃转发任务", "task_id", task.ID, "reason", err.Error())
		return nil // 返回nil表示任务处理完成（虽然是丢弃）
	}

	// 验证任务数据
	if err := task.Validate(); err != nil {
		logger.Error("任务数据验证失败",
			"task_id", task.ID,
			"original_message", task.OriginalMessage,
			"original_message_length", len(task.OriginalMessage),
			"source_channel", task.SourceChannel,
			"target_channels", task.TargetChannels,
			"author_id", task.AuthorID,
			"guild_id", task.GuildID,
			"error", err)
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("处理消息转发任务",
		"task_id", task.ID,
		"mapping", task.MappingName,
		"source_channel", task.SourceChannel,
		"target_channels", len(task.TargetChannels),
		"original_message_length", len(task.OriginalMessage))

	// 直接使用原始消息内容，如果为空则使用占位符
	processedMessage := task.OriginalMessage
	if strings.TrimSpace(processedMessage) == "" {
		processedMessage = "[无文本内容]" // 默认占位符
	}

	task.ProcessedMessage = processedMessage

	logger.Debug("消息处理完成",
		"task_id", task.ID,
		"original_length", len(task.OriginalMessage),
		"processed_length", len(task.ProcessedMessage),
		"processed_message", task.ProcessedMessage)

	// 发送消息到目标频道
	if err := h.sendToTargetChannels(ctx, client, task); err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	// 记录处理完成
	duration := time.Since(startTime)
	logger.Info("消息转发任务处理完成",
		"task_id", task.ID,
		"duration", duration,
		"target_channels", len(task.TargetChannels))

	return nil
}

// Validate 验证任务数据
func (h *MessageForwardTaskHandler) Validate(data any) error {
	if data == nil {
		return types.ErrInvalidTaskData
	}

	// 尝试解析任务数据
	_, err := ParseTaskFromData(data)
	return err
}

// convertEmbedsFromMap 将map格式的嵌入消息转换为discordgo.MessageEmbed
func (h *MessageForwardTaskHandler) convertEmbedsFromMap(embedsData []map[string]interface{}) []*discordgo.MessageEmbed {
	embeds := make([]*discordgo.MessageEmbed, len(embedsData))
	for i, embedData := range embedsData {
		embed := &discordgo.MessageEmbed{}

		// 基本字段
		if title, ok := embedData["title"].(string); ok {
			embed.Title = title
		}
		if description, ok := embedData["description"].(string); ok {
			embed.Description = description
		}
		if url, ok := embedData["url"].(string); ok {
			embed.URL = url
		}
		if color, ok := embedData["color"].(float64); ok {
			embed.Color = int(color)
		} else if color, ok := embedData["color"].(int); ok {
			embed.Color = color
		}
		if timestamp, ok := embedData["timestamp"].(string); ok {
			embed.Timestamp = timestamp
		}

		// 字段
		if fieldsRaw, ok := embedData["fields"].([]interface{}); ok {
			fields := make([]*discordgo.MessageEmbedField, len(fieldsRaw))
			for j, fieldRaw := range fieldsRaw {
				if fieldMap, ok := fieldRaw.(map[string]interface{}); ok {
					field := &discordgo.MessageEmbedField{}
					if name, ok := fieldMap["name"].(string); ok {
						field.Name = name
					}
					if value, ok := fieldMap["value"].(string); ok {
						field.Value = value
					}
					if inline, ok := fieldMap["inline"].(bool); ok {
						field.Inline = inline
					}
					fields[j] = field
				}
			}
			embed.Fields = fields
		}

		// 图片
		if imageRaw, ok := embedData["image"].(map[string]interface{}); ok {
			if imageURL, ok := imageRaw["url"].(string); ok {
				embed.Image = &discordgo.MessageEmbedImage{URL: imageURL}
			}
		}

		// 缩略图
		if thumbnailRaw, ok := embedData["thumbnail"].(map[string]interface{}); ok {
			if thumbnailURL, ok := thumbnailRaw["url"].(string); ok {
				embed.Thumbnail = &discordgo.MessageEmbedThumbnail{URL: thumbnailURL}
			}
		}

		// 作者
		if authorRaw, ok := embedData["author"].(map[string]interface{}); ok {
			author := &discordgo.MessageEmbedAuthor{}
			if name, ok := authorRaw["name"].(string); ok {
				author.Name = name
			}
			if url, ok := authorRaw["url"].(string); ok {
				author.URL = url
			}
			if iconURL, ok := authorRaw["icon_url"].(string); ok {
				author.IconURL = iconURL
			}
			embed.Author = author
		}

		// 页脚
		if footerRaw, ok := embedData["footer"].(map[string]interface{}); ok {
			footer := &discordgo.MessageEmbedFooter{}
			if text, ok := footerRaw["text"].(string); ok {
				footer.Text = text
			}
			if iconURL, ok := footerRaw["icon_url"].(string); ok {
				footer.IconURL = iconURL
			}
			embed.Footer = footer
		}

		embeds[i] = embed
	}
	return embeds
}

// sendToTargetChannels 发送消息到目标频道
func (h *MessageForwardTaskHandler) sendToTargetChannels(_ context.Context, client *types.Client, task *MessageForwardTask) error {
	if client.Session == nil {
		return fmt.Errorf("discord session 未初始化")
	}

	// 检查处理后的消息是否为空
	if strings.TrimSpace(task.ProcessedMessage) == "" {
		logger.Error("处理后的消息为空，无法发送",
			"task_id", task.ID,
			"original_message", task.OriginalMessage,
			"processed_message", task.ProcessedMessage)
		return fmt.Errorf("处理后的消息为空，Discord不允许发送空消息")
	}

	successCount := 0
	var lastError error

	for _, channelID := range task.TargetChannels {
		logger.Debug("发送消息到频道", "channel", channelID, "task_id", task.ID, "has_embeds", len(task.Embeds) > 0)

		// 构建消息发送数据
		messageSend := &discordgo.MessageSend{}

		// 检查是否有嵌入消息（优先使用JSON格式）
		hasEmbeds := task.EmbedsJSON != "" || len(task.Embeds) > 0

		if hasEmbeds {
			// 对于嵌入消息，不发送任何文本内容
			messageSend.Content = ""

			var embeds []*discordgo.MessageEmbed

			// 优先使用JSON格式的嵌入消息
			if task.EmbedsJSON != "" {
				var embedsFromJSON []*discordgo.MessageEmbed
				err := json.Unmarshal([]byte(task.EmbedsJSON), &embedsFromJSON)
				if err != nil {
					logger.Error("嵌入消息JSON反序列化失败", "error", err, "json", task.EmbedsJSON)
					// 回退到旧格式
					if len(task.Embeds) > 0 {
						embeds = h.convertEmbedsFromMap(task.Embeds)
					}
				} else {
					embeds = embedsFromJSON
					logger.Debug("使用JSON格式嵌入消息", "count", len(embeds))
				}
			} else if len(task.Embeds) > 0 {
				// 使用旧格式的嵌入消息
				embeds = h.convertEmbedsFromMap(task.Embeds)
				logger.Debug("使用旧格式嵌入消息", "count", len(embeds))
			}

			if len(embeds) > 0 {
				messageSend.Embeds = embeds
			}
		} else {
			// 没有嵌入消息时，发送处理后的文本内容
			messageSend.Content = task.ProcessedMessage
		}

		// 发送消息
		_, err := client.Session.ChannelMessageSendComplex(channelID, messageSend)
		if err != nil {
			logger.Error("发送消息到频道失败",
				"error", err,
				"channel", channelID,
				"task_id", task.ID)
			lastError = err
			continue
		}

		successCount++
		logger.Debug("消息发送成功", "channel", channelID, "task_id", task.ID)
	}

	// 检查发送结果
	if successCount == 0 {
		return fmt.Errorf("所有频道消息发送失败，最后错误: %w", lastError)
	}

	if successCount < len(task.TargetChannels) {
		logger.Warn("部分频道消息发送失败",
			"success", successCount,
			"total", len(task.TargetChannels),
			"task_id", task.ID)
	}

	logger.Info("消息转发完成",
		"task_id", task.ID,
		"success_count", successCount,
		"total_channels", len(task.TargetChannels))

	return nil
}

// replaceTemplateVar 替换模板变量
func replaceTemplateVar(template, key, value string) string {
	placeholder := "{" + key + "}"
	return replaceAll(template, placeholder, value)
}

// replaceAll 简单的字符串替换函数
func replaceAll(s, old, new string) string {
	// 简单实现，避免导入strings包
	result := ""
	for {
		index := findSubstring(s, old)
		if index == -1 {
			result += s
			break
		}
		result += s[:index] + new
		s = s[index+len(old):]
	}
	return result
}

// findSubstring 查找子字符串位置
func findSubstring(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// validateForwardRules 验证转发规则（优化：静默丢弃无效任务）
func (h *MessageForwardTaskHandler) validateForwardRules(task *MessageForwardTask) error {
	// 如果没有注入转发服务，静默丢弃任务
	if h.forwardService == nil {
		logger.Debug("转发服务未注入，丢弃转发任务", "task_id", task.ID)
		return fmt.Errorf("转发服务未注入") // 返回错误但不记录错误日志
	}

	// 验证每个目标频道是否有有效的转发规则
	for _, targetChannel := range task.TargetChannels {
		// 查找从源频道到目标频道的转发规则
		rules := h.forwardService.GetRulesBySourceChannel(task.SourceChannel)

		var validRule *types.ForwardRule
		for _, rule := range rules {
			if rule.GetTargetChannelID() == targetChannel && rule.IsEnabled() {
				validRule = rule
				break
			}
		}

		if validRule == nil {
			logger.Debug("未找到有效的转发规则，丢弃任务",
				"task_id", task.ID,
				"source_channel", task.SourceChannel,
				"target_channel", targetChannel)
			return fmt.Errorf("未找到有效的转发规则") // 返回错误但不记录错误日志
		}

		logger.Debug("转发规则验证通过",
			"task_id", task.ID,
			"rule_name", validRule.Name,
			"source_channel", task.SourceChannel,
			"target_channel", targetChannel)
	}

	logger.Debug("所有转发规则验证通过", "task_id", task.ID, "target_channels", len(task.TargetChannels))
	return nil
}
