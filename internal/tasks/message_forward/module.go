package message_forward

import (
	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"
)

// MessageForwardTaskModule 消息转发任务模块
type MessageForwardTaskModule struct {
	*tasks.BaseTaskModule
}

// NewMessageForwardTaskModule 创建新的消息转发任务模块
func NewMessageForwardTaskModule() *MessageForwardTaskModule {
	// 定义模块处理的队列
	queues := []string{"message_forward"}

	module := &MessageForwardTaskModule{
		BaseTaskModule: tasks.NewBaseTaskModule(
			"message_forward",
			"消息转发任务模块，处理频道间的延迟消息转发",
			queues,
		),
	}

	return module
}

// Initialize 初始化模块
func (m *MessageForwardTaskModule) Initialize(client *types.Client, queueService types.QueueService) error {
	logger.Info("📧 初始化消息转发任务模块...")

	// 创建并注册任务处理器
	forwardHandler := NewMessageForwardTaskHandler()
	m.BaseTaskModule.RegisterHandler(forwardHandler)

	logger.Debug("📝 消息转发任务处理器已注册", "type", forwardHandler.GetType())

	// 调用基础初始化
	if err := m.BaseTaskModule.Initialize(client, queueService); err != nil {
		return err
	}

	logger.Info("✅ 消息转发任务模块初始化完成")
	return nil
}

// Shutdown 关闭模块
func (m *MessageForwardTaskModule) Shutdown() error {
	logger.Info("正在关闭消息转发任务模块...")

	// 调用基础关闭
	if err := m.BaseTaskModule.Shutdown(); err != nil {
		return err
	}

	logger.Info("✅ 消息转发任务模块已关闭")
	return nil
}
