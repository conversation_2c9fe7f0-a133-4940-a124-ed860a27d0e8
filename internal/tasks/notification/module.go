package notification

import (
	"fmt"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// NotificationTaskModule 通知任务模块
type NotificationTaskModule struct {
	*tasks.BaseTaskModule
	session  *discordgo.Session
	handlers map[string]tasks.TaskHandler
}

// NewNotificationTaskModule 创建通知任务模块
func NewNotificationTaskModule() *NotificationTaskModule {
	base := tasks.NewBaseTaskModule(
		"notification",
		"处理通知相关任务",
		[]string{"notification", "notification.channel", "notification.user"},
	)

	module := &NotificationTaskModule{
		BaseTaskModule: base,
		handlers:       make(map[string]tasks.TaskHandler),
	}

	return module
}

// Initialize 初始化模块
func (m *NotificationTaskModule) Initialize(client *types.Client, queueService types.QueueService) error {
	logger.Info("📧 初始化通知任务模块...")

	// 保存 Discord session
	if client != nil && client.Session != nil {
		m.session = client.Session
	} else {
		return fmt.Errorf("Discord session 未初始化")
	}

	// 创建并注册任务处理器
	channelHandler := NewChannelNotificationHandler(m.session)
	userHandler := NewUserNotificationHandler(m.session)
	generalHandler := NewGeneralNotificationHandler(m.session)

	m.RegisterHandler(channelHandler)
	m.RegisterHandler(userHandler)
	m.RegisterHandler(generalHandler)

	// 调用基础模块初始化
	if err := m.BaseTaskModule.Initialize(client, queueService); err != nil {
		return fmt.Errorf("基础模块初始化失败: %w", err)
	}

	logger.Info("✅ 通知任务模块初始化完成")
	return nil
}

// GetHandlers 获取任务处理器映射
func (m *NotificationTaskModule) GetHandlers() map[string]tasks.TaskHandler {
	return m.BaseTaskModule.GetHandlers()
}

// Shutdown 关闭模块
func (m *NotificationTaskModule) Shutdown() error {
	logger.Info("📧 关闭通知任务模块...")

	// 清理资源
	m.session = nil
	m.handlers = nil

	// 调用基础模块关闭
	if err := m.BaseTaskModule.Shutdown(); err != nil {
		return fmt.Errorf("基础模块关闭失败: %w", err)
	}

	logger.Info("✅ 通知任务模块已关闭")
	return nil
}

// NotificationTaskData 通知任务数据结构
type NotificationTaskData struct {
	ID      string                 `json:"id"`
	Type    string                 `json:"type"`    // channel, user, general
	Target  string                 `json:"target"`  // channel ID or user ID
	Content string                 `json:"content"` // 消息内容
	Options map[string]interface{} `json:"options"` // 额外选项
	Payload map[string]interface{} `json:"payload"` // 完整载荷
}

// Validate 验证通知任务数据
func (data *NotificationTaskData) Validate() error {
	if data.Type == "" {
		return fmt.Errorf("通知类型不能为空")
	}

	if data.Target == "" {
		return fmt.Errorf("通知目标不能为空")
	}

	if data.Content == "" && (data.Options == nil || data.Options["embeds"] == nil) {
		return fmt.Errorf("通知内容不能为空")
	}

	return nil
}

// ParseNotificationData 解析通知任务数据
func ParseNotificationData(data interface{}) (*NotificationTaskData, error) {
	// 尝试直接类型断言
	if taskData, ok := data.(*NotificationTaskData); ok {
		return taskData, nil
	}

	// 尝试从 map 解析
	if dataMap, ok := data.(map[string]interface{}); ok {
		taskData := &NotificationTaskData{}

		if id, exists := dataMap["id"]; exists {
			if idStr, ok := id.(string); ok {
				taskData.ID = idStr
			}
		}

		if taskType, exists := dataMap["type"]; exists {
			if typeStr, ok := taskType.(string); ok {
				taskData.Type = typeStr
			}
		}

		if target, exists := dataMap["target"]; exists {
			if targetStr, ok := target.(string); ok {
				taskData.Target = targetStr
			}
		}

		if content, exists := dataMap["content"]; exists {
			if contentStr, ok := content.(string); ok {
				taskData.Content = contentStr
			}
		}

		if options, exists := dataMap["options"]; exists {
			if optionsMap, ok := options.(map[string]interface{}); ok {
				taskData.Options = optionsMap
			}
		}

		if payload, exists := dataMap["payload"]; exists {
			if payloadMap, ok := payload.(map[string]interface{}); ok {
				taskData.Payload = payloadMap
			}
		}

		return taskData, nil
	}

	return nil, fmt.Errorf("无法解析通知任务数据: %T", data)
}
