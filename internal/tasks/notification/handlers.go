package notification

import (
	"context"
	"fmt"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"
)

// ChannelNotificationHandler 频道通知处理器
type ChannelNotificationHandler struct {
	*tasks.BaseTaskHandler
	session *discordgo.Session
}

// NewChannelNotificationHandler 创建频道通知处理器
func NewChannelNotificationHandler(session *discordgo.Session) *ChannelNotificationHandler {
	return &ChannelNotificationHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"notification.channel",
			"处理频道通知任务",
		),
		session: session,
	}
}

// Handle 处理频道通知任务
func (h *ChannelNotificationHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseNotificationData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	if err := taskData.Validate(); err != nil {
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("📧 处理频道通知任务", "id", taskData.ID, "channel", taskData.Target)

	// 获取频道
	channel, err := h.session.Channel(taskData.Target)
	if err != nil {
		return fmt.Errorf("获取频道失败 %s: %w", taskData.Target, err)
	}

	// 检查频道类型
	if !isTextChannel(channel) {
		return fmt.Errorf("频道 %s 不支持发送消息", taskData.Target)
	}

	// 构建消息
	messageData := &discordgo.MessageSend{
		Content: taskData.Content,
	}

	// 处理额外选项
	if taskData.Options != nil {
		if embeds, exists := taskData.Options["embeds"]; exists {
			if embedsSlice, ok := embeds.([]interface{}); ok {
				messageData.Embeds = parseEmbeds(embedsSlice)
			}
		}

		if components, exists := taskData.Options["components"]; exists {
			if componentsSlice, ok := components.([]interface{}); ok {
				messageData.Components = parseComponents(componentsSlice)
			}
		}
	}

	// 发送消息
	message, err := h.session.ChannelMessageSendComplex(taskData.Target, messageData)
	if err != nil {
		return fmt.Errorf("发送频道消息失败: %w", err)
	}

	logger.Info("✅ 频道通知发送成功", 
		"id", taskData.ID, 
		"channel", taskData.Target, 
		"message_id", message.ID)

	return nil
}

// UserNotificationHandler 用户通知处理器
type UserNotificationHandler struct {
	*tasks.BaseTaskHandler
	session *discordgo.Session
}

// NewUserNotificationHandler 创建用户通知处理器
func NewUserNotificationHandler(session *discordgo.Session) *UserNotificationHandler {
	return &UserNotificationHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"notification.user",
			"处理用户私信通知任务",
		),
		session: session,
	}
}

// Handle 处理用户通知任务
func (h *UserNotificationHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseNotificationData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	if err := taskData.Validate(); err != nil {
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("📧 处理用户通知任务", "id", taskData.ID, "user", taskData.Target)

	// 创建私信频道
	dmChannel, err := h.session.UserChannelCreate(taskData.Target)
	if err != nil {
		return fmt.Errorf("创建私信频道失败 %s: %w", taskData.Target, err)
	}

	// 构建消息
	messageData := &discordgo.MessageSend{
		Content: taskData.Content,
	}

	// 处理额外选项
	if taskData.Options != nil {
		if embeds, exists := taskData.Options["embeds"]; exists {
			if embedsSlice, ok := embeds.([]interface{}); ok {
				messageData.Embeds = parseEmbeds(embedsSlice)
			}
		}
	}

	// 发送私信
	message, err := h.session.ChannelMessageSendComplex(dmChannel.ID, messageData)
	if err != nil {
		return fmt.Errorf("发送用户私信失败: %w", err)
	}

	logger.Info("✅ 用户通知发送成功", 
		"id", taskData.ID, 
		"user", taskData.Target, 
		"message_id", message.ID)

	return nil
}

// GeneralNotificationHandler 通用通知处理器
type GeneralNotificationHandler struct {
	*tasks.BaseTaskHandler
	session *discordgo.Session
}

// NewGeneralNotificationHandler 创建通用通知处理器
func NewGeneralNotificationHandler(session *discordgo.Session) *GeneralNotificationHandler {
	return &GeneralNotificationHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"notification",
			"处理通用通知任务",
		),
		session: session,
	}
}

// Handle 处理通用通知任务
func (h *GeneralNotificationHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	taskData, err := ParseNotificationData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	logger.Info("📧 处理通用通知任务", "id", taskData.ID, "type", taskData.Type)

	// 根据类型分发到具体处理器
	switch taskData.Type {
	case "channel":
		channelHandler := NewChannelNotificationHandler(h.session)
		return channelHandler.Handle(ctx, client, data)
	case "user":
		userHandler := NewUserNotificationHandler(h.session)
		return userHandler.Handle(ctx, client, data)
	default:
		return fmt.Errorf("不支持的通知类型: %s", taskData.Type)
	}
}

// 辅助函数

// isTextChannel 检查是否为文本频道
func isTextChannel(channel *discordgo.Channel) bool {
	return channel.Type == discordgo.ChannelTypeGuildText ||
		channel.Type == discordgo.ChannelTypeDM ||
		channel.Type == discordgo.ChannelTypeGroupDM ||
		channel.Type == discordgo.ChannelTypeGuildNews ||
		channel.Type == discordgo.ChannelTypeGuildNewsThread ||
		channel.Type == discordgo.ChannelTypeGuildPublicThread ||
		channel.Type == discordgo.ChannelTypeGuildPrivateThread
}

// parseEmbeds 解析嵌入消息
func parseEmbeds(embedsData []interface{}) []*discordgo.MessageEmbed {
	var embeds []*discordgo.MessageEmbed

	for _, embedData := range embedsData {
		if embedMap, ok := embedData.(map[string]interface{}); ok {
			embed := &discordgo.MessageEmbed{}

			if title, exists := embedMap["title"]; exists {
				if titleStr, ok := title.(string); ok {
					embed.Title = titleStr
				}
			}

			if description, exists := embedMap["description"]; exists {
				if descStr, ok := description.(string); ok {
					embed.Description = descStr
				}
			}

			if color, exists := embedMap["color"]; exists {
				if colorInt, ok := color.(int); ok {
					embed.Color = colorInt
				}
			}

			// 可以继续添加更多字段解析...

			embeds = append(embeds, embed)
		}
	}

	return embeds
}

// parseComponents 解析消息组件
func parseComponents(componentsData []interface{}) []discordgo.MessageComponent {
	var components []discordgo.MessageComponent

	// 这里可以添加组件解析逻辑
	// 暂时返回空切片

	return components
}
