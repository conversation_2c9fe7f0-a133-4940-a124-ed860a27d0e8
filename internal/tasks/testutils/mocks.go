package testutils

import (
	"context"
	"time"

	"zeka-go/internal/types"
)

// MockQueueService 模拟队列服务
type MockQueueService struct {
	handlers map[string]func(interface{}) error
	stats    types.QueueStats
}

func NewMockQueueService() *MockQueueService {
	return &MockQueueService{
		handlers: make(map[string]func(interface{}) error),
		stats: types.QueueStats{
			Published: 0,
			Consumed:  0,
		},
	}
}

// 连接管理
func (m *MockQueueService) Connect(ctx context.Context) error { return nil }
func (m *MockQueueService) Close() error                      { return nil }
func (m *MockQueueService) IsConnected() bool                 { return true }

// 队列管理
func (m *MockQueueService) DeclareQueue(ctx context.Context, name string, options types.QueueOptions) error {
	return nil
}
func (m *MockQueueService) DeleteQueue(ctx context.Context, name string, options types.DeleteQueueOptions) error {
	return nil
}

// 交换机管理
func (m *MockQueueService) DeclareExchange(ctx context.Context, name, kind string, options types.ExchangeOptions) error {
	return nil
}
func (m *MockQueueService) DeleteExchange(ctx context.Context, name string, options types.DeleteExchangeOptions) error {
	return nil
}

// 绑定管理
func (m *MockQueueService) BindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	return nil
}
func (m *MockQueueService) UnbindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	return nil
}

// 消息发布
func (m *MockQueueService) Publish(ctx context.Context, exchange, routingKey string, message []byte, options types.PublishOptions) error {
	m.stats.Published++
	return nil
}
func (m *MockQueueService) PublishJSON(ctx context.Context, exchange, routingKey string, message interface{}, options types.PublishOptions) error {
	m.stats.Published++
	return nil
}

// 消息消费
func (m *MockQueueService) Consume(ctx context.Context, queueName string, handler types.MessageHandler, options types.ConsumeOptions) error {
	return nil
}

// 任务管理
func (m *MockQueueService) PublishTask(ctx context.Context, queueName, taskType string, data interface{}, options types.PublishOptions) (string, error) {
	m.stats.Published++
	return "mock_task_id", nil
}
func (m *MockQueueService) PublishDelayedTask(ctx context.Context, queueName, taskType string, data interface{}, delay time.Duration, options types.PublishOptions) (string, error) {
	m.stats.Published++
	return "mock_delayed_task_id", nil
}
func (m *MockQueueService) RegisterTaskHandler(taskType string, handler types.TaskHandler) {
	// 模拟注册
}
func (m *MockQueueService) StartTaskConsumer(ctx context.Context, queueName string, options types.ConsumeOptions) error {
	return nil
}

// 简化的队列操作（兼容现有代码）
func (m *MockQueueService) RegisterHandler(taskType string, handler func(interface{}) error) {
	m.handlers[taskType] = handler
}
func (m *MockQueueService) StartConsumer(queueName string, options interface{}) error {
	return nil
}

// 监控
func (m *MockQueueService) GetQueueInfo(ctx context.Context, queueName string) (*types.QueueInfo, error) {
	return &types.QueueInfo{}, nil
}
func (m *MockQueueService) GetStats() *types.QueueStats {
	return &m.stats
}

// 测试辅助方法
func (m *MockQueueService) TriggerHandler(taskType string, data interface{}) error {
	if handler, exists := m.handlers[taskType]; exists {
		m.stats.Consumed++
		return handler(data)
	}
	return nil
}

// FailingMockQueueService 会失败的模拟队列服务
type FailingMockQueueService struct {
	*MockQueueService
}

func NewFailingMockQueueService() *FailingMockQueueService {
	return &FailingMockQueueService{
		MockQueueService: NewMockQueueService(),
	}
}

func (f *FailingMockQueueService) StartConsumer(queueName string, options interface{}) error {
	return types.ErrQueueNotAvailable
}

func (f *FailingMockQueueService) RegisterHandler(taskType string, handler func(interface{}) error) {
	// 模拟注册失败，但不返回错误（因为接口没有返回值）
}
