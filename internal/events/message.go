package events

import (
	"context"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// MessageCreateHandler 消息创建事件处理器
type MessageCreateHandler struct {
	eventType string
	priority  int
	config    *types.Config

	// 服务依赖（运行时注入）
	forwardService types.ForwardRuleManager
}

// NewMessageCreateHandler 创建消息创建事件处理器
func NewMessageCreateHandler(config *types.Config) *MessageCreateHandler {
	return &MessageCreateHandler{
		eventType: "MESSAGE_CREATE",
		priority:  0,
		config:    config,
	}
}

// SetForwardService 设置转发服务（运行时注入）
func (h *MessageCreateHandler) SetForwardService(forwardService types.ForwardRuleManager) {
	h.forwardService = forwardService
}

// GetEventType 获取事件类型
func (h *MessageCreateHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *MessageCreateHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *MessageCreateHandler) ShouldHandle(event interface{}) bool {
	message, ok := event.(*discordgo.MessageCreate)
	if !ok {
		return false
	}

	// 忽略机器人消息
	if message.Author.Bot {
		return false
	}

	// 忽略系统消息
	if message.Type != discordgo.MessageTypeDefault && message.Type != discordgo.MessageTypeReply {
		return false
	}

	return true
}

// Handle 处理事件
func (h *MessageCreateHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	message, ok := event.(*discordgo.MessageCreate)
	if !ok {
		return nil
	}

	logger.Debug("处理消息创建事件",
		"user", message.Author.Username,
		"channel", message.ChannelID,
		"guild", message.GuildID,
		"content_length", len(message.Content))

	// 检查是否为前缀命令
	if h.isPrefixCommand(message.Content) {
		return h.handlePrefixCommand(ctx, client, message)
	}

	// 检查是否提及了 Bot
	// if h.isBotMentioned(client.Session.State.User.ID, message.Mentions) {
	// 	return h.handleMention(ctx, client, message)
	// }

	// 处理自动回复
	// if h.shouldAutoReply(message) {
	// 	return h.handleAutoReply(ctx, client, message)
	// }

	// 检查转发规则（关键修复：添加转发规则匹配）
	if h.forwardService != nil {
		return h.handleForwardRules(ctx, client, message)
	}

	return nil
}

// isPrefixCommand 检查是否为前缀命令
func (h *MessageCreateHandler) isPrefixCommand(content string) bool {
	if h.config.Discord.Prefix == "" {
		return false
	}

	return strings.HasPrefix(content, h.config.Discord.Prefix)
}

// handlePrefixCommand 处理前缀命令
func (h *MessageCreateHandler) handlePrefixCommand(ctx context.Context, client *types.Client, message *discordgo.MessageCreate) error {
	content := strings.TrimPrefix(message.Content, h.config.Discord.Prefix)
	parts := strings.Fields(content)

	if len(parts) == 0 {
		return nil
	}

	commandName := strings.ToLower(parts[0])

	logger.Info("处理前缀命令",
		"command", commandName,
		"user", message.Author.Username,
		"guild", message.GuildID)

	// 这里可以添加前缀命令的处理逻辑
	// 目前主要使用斜杠命令，前缀命令作为备用

	return nil
}

// isBotMentioned 检查是否提及了 Bot
func (h *MessageCreateHandler) isBotMentioned(botID string, mentions []*discordgo.User) bool {
	for _, mention := range mentions {
		if mention.ID == botID {
			return true
		}
	}
	return false
}

// handleMention 处理提及
func (h *MessageCreateHandler) handleMention(ctx context.Context, client *types.Client, message *discordgo.MessageCreate) error {
	logger.Info("Bot 被提及",
		"user", message.Author.Username,
		"guild", message.GuildID,
		"channel", message.ChannelID)

	// 简单的提及回复
	embed := &discordgo.MessageEmbed{
		Title:       "👋 你好！",
		Description: "我是 Zeka Bot！使用 `/help` 查看可用命令。",
		Color:       0x3498db,
		Footer: &discordgo.MessageEmbedFooter{
			Text: "Zeka Bot - Go 版本",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err := client.Session.ChannelMessageSendEmbed(message.ChannelID, embed)
	return err
}

// shouldAutoReply 检查是否应该自动回复
func (h *MessageCreateHandler) shouldAutoReply(message *discordgo.MessageCreate) bool {
	content := strings.ToLower(message.Content)

	// 检查一些关键词
	keywords := []string{"hello", "hi", "你好", "嗨"}
	for _, keyword := range keywords {
		if strings.Contains(content, keyword) {
			return true
		}
	}

	return false
}

// handleAutoReply 处理自动回复
func (h *MessageCreateHandler) handleAutoReply(ctx context.Context, client *types.Client, message *discordgo.MessageCreate) error {
	content := strings.ToLower(message.Content)

	var reply string
	if strings.Contains(content, "hello") || strings.Contains(content, "hi") {
		reply = "Hello there! 👋"
	} else if strings.Contains(content, "你好") || strings.Contains(content, "嗨") {
		reply = "你好！👋"
	}

	if reply != "" {
		_, err := client.Session.ChannelMessageSend(message.ChannelID, reply)
		return err
	}

	return nil
}

// handleForwardRules 处理转发规则（关键修复：实现转发规则匹配）
func (h *MessageCreateHandler) handleForwardRules(ctx context.Context, client *types.Client, message *discordgo.MessageCreate) error {
	logger.Info("🔍 检查转发规则",
		"channel_id", message.ChannelID,
		"forward_service_available", h.forwardService != nil,
		"message_id", message.ID,
		"author", message.Author.Username,
		"content_preview", func() string {
			if len(message.Content) > 50 {
				return message.Content[:50] + "..."
			}
			return message.Content
		}())

	// 获取该频道的转发规则
	rules := h.forwardService.GetRulesBySourceChannel(message.ChannelID)
	if len(rules) == 0 {
		logger.Debug("📭 没有匹配的转发规则", "channel_id", message.ChannelID)
		return nil
	}

	logger.Info("✅ 找到转发规则",
		"channel_id", message.ChannelID,
		"rules_count", len(rules),
		"message_content_length", len(message.Content),
		"message_id", message.ID)

	// 遍历所有匹配的规则
	for i, rule := range rules {
		logger.Info("🔄 处理转发规则",
			"rule_index", i+1,
			"rule_name", rule.Name,
			"rule_enabled", rule.Enabled,
			"source_channel", rule.GetSourceChannelID(),
			"target_channel", rule.GetTargetChannelID())

		// 检查规则是否启用
		if !rule.Enabled {
			logger.Warn("⏭️ 跳过已禁用的转发规则", "rule_name", rule.Name)
			continue
		}

		// 检查是否应该转发此消息
		shouldForward, err := h.forwardService.ShouldForward(rule, message)
		if err != nil {
			logger.Error("❌ 检查转发条件失败",
				"rule_name", rule.Name,
				"error", err)
			continue
		}

		if !shouldForward {
			logger.Info("⏭️ 消息不满足转发条件", "rule_name", rule.Name)
			continue
		}

		logger.Info("🚀 开始执行转发",
			"rule_name", rule.Name,
			"message_id", message.ID)

		// 执行转发
		if err := h.forwardService.ForwardMessage(rule, message); err != nil {
			logger.Error("❌ 转发消息失败",
				"rule_name", rule.Name,
				"source_channel", message.ChannelID,
				"target_channel", rule.GetTargetChannelID(),
				"message_id", message.ID,
				"error", err)
			// 继续处理其他规则，不因为一个规则失败而停止
			continue
		}

		logger.Info("✅ 转发消息成功",
			"rule_name", rule.Name,
			"message_id", message.ID)

		logger.Info("消息转发成功",
			"rule_name", rule.Name,
			"source_channel", message.ChannelID,
			"target_channel", rule.GetTargetChannelID(),
			"message_id", message.ID)
	}

	return nil
}

// MessageUpdateHandler 消息更新事件处理器
type MessageUpdateHandler struct {
	eventType string
	priority  int
}

// NewMessageUpdateHandler 创建消息更新事件处理器
func NewMessageUpdateHandler() *MessageUpdateHandler {
	return &MessageUpdateHandler{
		eventType: "MESSAGE_UPDATE",
		priority:  0,
	}
}

// GetEventType 获取事件类型
func (h *MessageUpdateHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *MessageUpdateHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *MessageUpdateHandler) ShouldHandle(event interface{}) bool {
	message, ok := event.(*discordgo.MessageUpdate)
	if !ok {
		return false
	}

	// 忽略机器人消息
	if message.Author != nil && message.Author.Bot {
		return false
	}

	return true
}

// Handle 处理事件
func (h *MessageUpdateHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	message, ok := event.(*discordgo.MessageUpdate)
	if !ok {
		return nil
	}

	logger.Debug("处理消息更新事件",
		"message_id", message.ID,
		"channel", message.ChannelID,
		"guild", message.GuildID)

	// 这里可以添加消息更新的处理逻辑
	// 例如：记录编辑历史、检查违规内容等

	return nil
}

// MessageDeleteHandler 消息删除事件处理器
type MessageDeleteHandler struct {
	eventType string
	priority  int
}

// NewMessageDeleteHandler 创建消息删除事件处理器
func NewMessageDeleteHandler() *MessageDeleteHandler {
	return &MessageDeleteHandler{
		eventType: "MESSAGE_DELETE",
		priority:  0,
	}
}

// GetEventType 获取事件类型
func (h *MessageDeleteHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *MessageDeleteHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *MessageDeleteHandler) ShouldHandle(event interface{}) bool {
	_, ok := event.(*discordgo.MessageDelete)
	return ok
}

// Handle 处理事件
func (h *MessageDeleteHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	message, ok := event.(*discordgo.MessageDelete)
	if !ok {
		return nil
	}

	logger.Debug("处理消息删除事件",
		"message_id", message.ID,
		"channel", message.ChannelID,
		"guild", message.GuildID)

	// 这里可以添加消息删除的处理逻辑
	// 例如：记录删除日志、备份重要消息等

	return nil
}
