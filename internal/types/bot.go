package types

import (
	"context"
	"sync"
	"time"

	"github.com/bwmarrin/discordgo"
)

// Bot 接口定义了 Discord Bot 的核心功能
type Bot interface {
	Start(ctx context.Context) error
	Shutdown(ctx context.Context) error
	GetSession() *discordgo.Session
	GetConfig() *Config
}

// ClientContext 客户端上下文信息
type ClientContext struct {
	Config    *Config
	UserID    string
	GuildID   string
	ChannelID string
	RequestID string
}

// ServiceContainer 服务容器
type ServiceContainer struct {
	CacheService        CacheService
	QueueService        QueueService
	NotificationService NotificationService
}

// HandlerContainer 处理器容器
type HandlerContainer struct {
	Commands   map[string]Command
	Events     map[string]EventHandler
	Components map[string]ComponentHandler
}

// CooldownManager 冷却时间管理器
type CooldownManager struct {
	Cooldowns map[string]map[string]time.Time
	mu        sync.RWMutex
}

// NewCooldownManager 创建冷却时间管理器
func NewCooldownManager() *CooldownManager {
	return &CooldownManager{
		Cooldowns: make(map[string]map[string]time.Time),
	}
}

// SetCooldown 设置冷却时间
func (cm *CooldownManager) SetCooldown(userID, command string, duration time.Duration) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if cm.Cooldowns[userID] == nil {
		cm.Cooldowns[userID] = make(map[string]time.Time)
	}

	cm.Cooldowns[userID][command] = time.Now().Add(duration)
}

// IsOnCooldown 检查是否在冷却时间内
func (cm *CooldownManager) IsOnCooldown(userID, command string) bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	userCooldowns, exists := cm.Cooldowns[userID]
	if !exists {
		return false
	}

	cooldownTime, exists := userCooldowns[command]
	if !exists {
		return false
	}

	return time.Now().Before(cooldownTime)
}

// GetRemainingCooldown 获取剩余冷却时间
func (cm *CooldownManager) GetRemainingCooldown(userID, command string) time.Duration {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	userCooldowns, exists := cm.Cooldowns[userID]
	if !exists {
		return 0
	}

	cooldownTime, exists := userCooldowns[command]
	if !exists {
		return 0
	}

	remaining := time.Until(cooldownTime)
	if remaining < 0 {
		return 0
	}

	return remaining
}

// Client 简化的客户端结构 - 主要作为Discord会话的包装器
type Client struct {
	*discordgo.Session
	Context         *ClientContext
	Services        *ServiceContainer
	Handlers        *HandlerContainer
	CooldownManager *CooldownManager
}

// NewClient 创建新的客户端实例
func NewClient(session *discordgo.Session, config *Config) *Client {
	return &Client{
		Session: session,
		Context: &ClientContext{
			Config: config,
		},
		Services: &ServiceContainer{},
		Handlers: &HandlerContainer{
			Commands:   make(map[string]Command),
			Events:     make(map[string]EventHandler),
			Components: make(map[string]ComponentHandler),
		},
		CooldownManager: NewCooldownManager(),
	}
}

// UpdateContext 更新客户端上下文
func (c *Client) UpdateContext(userID, guildID, channelID, requestID string) {
	if c.Context == nil {
		c.Context = &ClientContext{}
	}

	c.Context.UserID = userID
	c.Context.GuildID = guildID
	c.Context.ChannelID = channelID
	c.Context.RequestID = requestID
}

// GetCacheService 获取缓存服务
func (c *Client) GetCacheService() CacheService {
	if c.Services == nil {
		return nil
	}
	return c.Services.CacheService
}

// GetQueueService 获取队列服务
func (c *Client) GetQueueService() QueueService {
	if c.Services == nil {
		return nil
	}
	return c.Services.QueueService
}

// GetNotificationService 获取通知服务
func (c *Client) GetNotificationService() NotificationService {
	if c.Services == nil {
		return nil
	}
	return c.Services.NotificationService
}

// Command 定义命令接口
type Command interface {
	GetName() string
	GetDescription() string
	GetCategory() string
	GetCooldown() time.Duration
	GetPermissions() []string
	Execute(ctx context.Context, client *Client, interaction *discordgo.InteractionCreate) error
	Validate(interaction *discordgo.InteractionCreate) error
}

// EventHandler 定义事件处理器接口
type EventHandler interface {
	GetEventType() string
	GetPriority() int
	Handle(ctx context.Context, client *Client, event interface{}) error
	ShouldHandle(event interface{}) bool
}

// ComponentHandler 定义组件处理器接口
type ComponentHandler interface {
	GetCustomID() string
	GetType() ComponentType
	Handle(ctx context.Context, client *Client, interaction *discordgo.InteractionCreate) error
	Validate(interaction *discordgo.InteractionCreate) error
}

// ComponentType 定义组件类型
type ComponentType int

const (
	ComponentTypeButton ComponentType = iota
	ComponentTypeSelectMenu
	ComponentTypeModal
)

// String 返回组件类型的字符串表示
func (ct ComponentType) String() string {
	switch ct {
	case ComponentTypeButton:
		return "button"
	case ComponentTypeSelectMenu:
		return "select_menu"
	case ComponentTypeModal:
		return "modal"
	default:
		return "unknown"
	}
}

// Middleware 定义中间件接口
type Middleware interface {
	Process(ctx context.Context, next func(context.Context) error) error
}

// MiddlewareFunc 中间件函数类型
type MiddlewareFunc func(ctx context.Context, next func(context.Context) error) error

// Process 实现 Middleware 接口
func (mf MiddlewareFunc) Process(ctx context.Context, next func(context.Context) error) error {
	return mf(ctx, next)
}
