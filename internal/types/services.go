package types

import (
	"context"
	"time"
)

// CacheService 缓存服务接口
type CacheService interface {
	// 基础操作
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Del(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, keys ...string) (int64, error)

	// 哈希操作
	HGet(ctx context.Context, key, field string) (string, error)
	HSet(ctx context.Context, key string, values ...interface{}) error
	HGetAll(ctx context.Context, key string) (map[string]string, error)
	HDel(ctx context.Context, key string, fields ...string) error

	// 列表操作
	LPush(ctx context.Context, key string, values ...interface{}) error
	RPush(ctx context.Context, key string, values ...interface{}) error
	LPop(ctx context.Context, key string) (string, error)
	RPop(ctx context.Context, key string) (string, error)
	LLen(ctx context.Context, key string) (int64, error)

	// 集合操作
	SAdd(ctx context.Context, key string, members ...interface{}) error
	SRem(ctx context.Context, key string, members ...interface{}) error
	SMembers(ctx context.Context, key string) ([]string, error)
	SIsMember(ctx context.Context, key string, member interface{}) (bool, error)

	// 有序集合操作
	ZAdd(ctx context.Context, key string, members ...interface{}) error
	ZRem(ctx context.Context, key string, members ...interface{}) error
	ZRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	ZScore(ctx context.Context, key, member string) (float64, error)

	// 过期时间
	Expire(ctx context.Context, key string, expiration time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)

	// 连接管理
	Ping(ctx context.Context) error
	Close() error
}

// QueueService 队列服务接口
type QueueService interface {
	// 连接管理
	Connect(ctx context.Context) error
	Close() error
	IsConnected() bool

	// 队列管理
	DeclareQueue(ctx context.Context, name string, options QueueOptions) error
	DeleteQueue(ctx context.Context, name string, options DeleteQueueOptions) error

	// 交换机管理
	DeclareExchange(ctx context.Context, name, kind string, options ExchangeOptions) error
	DeleteExchange(ctx context.Context, name string, options DeleteExchangeOptions) error

	// 绑定管理
	BindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error
	UnbindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error

	// 消息发布
	Publish(ctx context.Context, exchange, routingKey string, message []byte, options PublishOptions) error
	PublishJSON(ctx context.Context, exchange, routingKey string, message interface{}, options PublishOptions) error

	// 消息消费
	Consume(ctx context.Context, queueName string, handler MessageHandler, options ConsumeOptions) error

	// 任务管理
	PublishTask(ctx context.Context, queueName, taskType string, data interface{}, options PublishOptions) (string, error)
	PublishDelayedTask(ctx context.Context, queueName, taskType string, data interface{}, delay time.Duration, options PublishOptions) (string, error)
	RegisterTaskHandler(taskType string, handler TaskHandler)
	StartTaskConsumer(ctx context.Context, queueName string, options ConsumeOptions) error

	// 简化的队列操作（兼容现有代码）
	RegisterHandler(taskType string, handler func(data interface{}) error)
	StartConsumer(queueName string, options interface{}) error

	// 监控
	GetQueueInfo(ctx context.Context, queueName string) (*QueueInfo, error)
	GetStats() *QueueStats
}

// NotificationService 通知服务接口
type NotificationService interface {
	// 初始化
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error

	// 模板管理
	LoadTemplate(templateID string, guildID ...string) (*Template, error)
	ReloadTemplates() error
	ValidateTemplate(template *Template) error

	// 通知发送
	SendNotification(ctx context.Context, options NotificationOptions) (*NotificationResult, error)
	SendBulkNotifications(ctx context.Context, notifications []NotificationOptions) ([]*NotificationResult, error)

	// 消息跟踪
	TrackMessage(messageID string, metadata MessageMetadata) error
	GetMessageStatus(messageID string) (*MessageStatus, error)
	UpdateMessageStatus(messageID string, status string, metadata map[string]interface{}) error

	// 触发器系统
	RegisterTrigger(trigger *Trigger) error
	UnregisterTrigger(triggerID string) error
	ProcessEvent(ctx context.Context, event *TriggerEvent) error
}

// LoggerService 日志服务接口
type LoggerService interface {
	// 基础日志方法
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})

	// 格式化日志方法
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
	Fatalf(format string, args ...interface{})

	// 带上下文的日志方法
	DebugContext(ctx context.Context, msg string, fields ...interface{})
	InfoContext(ctx context.Context, msg string, fields ...interface{})
	WarnContext(ctx context.Context, msg string, fields ...interface{})
	ErrorContext(ctx context.Context, msg string, fields ...interface{})

	// 结构化字段
	With(fields ...interface{}) LoggerService
	WithContext(ctx context.Context) LoggerService
	WithError(err error) LoggerService

	// 配置管理
	SetLevel(level string) error
	GetLevel() string

	// 关闭
	Sync() error
}

// 队列相关选项和类型
type QueueOptions struct {
	Durable    bool
	AutoDelete bool
	Exclusive  bool
	NoWait     bool
	Arguments  map[string]interface{}
}

type DeleteQueueOptions struct {
	IfUnused bool
	IfEmpty  bool
	NoWait   bool
}

type DeleteExchangeOptions struct {
	IfUnused bool
	NoWait   bool
}

type PublishOptions struct {
	Mandatory bool
	Immediate bool
	Headers   map[string]interface{}
	Priority  uint8
	TTL       time.Duration
}

type ConsumeOptions struct {
	Consumer  string
	AutoAck   bool
	Exclusive bool
	NoLocal   bool
	NoWait    bool
	Arguments map[string]interface{}
}

// 消息处理器类型
type MessageHandler func(ctx context.Context, message *Message) error
type TaskHandler func(ctx context.Context, data []byte) error

// 消息结构
type Message struct {
	ID          string
	Body        []byte
	Headers     map[string]interface{}
	ContentType string
	Timestamp   time.Time
	ReplyTo     string
	Priority    uint8
}

// 队列信息
type QueueInfo struct {
	Name       string
	Messages   int
	Consumers  int
	Durable    bool
	AutoDelete bool
	Exclusive  bool
	Arguments  map[string]interface{}
}

// 队列统计
type QueueStats struct {
	Published   int64
	Consumed    int64
	Failed      int64
	Requeued    int64
	LastUpdated time.Time
}

// 队列健康状态
type QueueHealthStatus struct {
	Status            string     `json:"status"`             // "healthy", "unhealthy", "degraded"
	Message           string     `json:"message"`            // 状态描述
	Connected         bool       `json:"connected"`          // 是否连接
	URL               string     `json:"url"`                // 连接URL（隐藏敏感信息）
	ReconnectAttempts int        `json:"reconnect_attempts"` // 重连尝试次数
	MaxRetries        int        `json:"max_retries"`        // 最大重试次数
	Stats             QueueStats `json:"stats"`              // 统计信息
	LastCheck         time.Time  `json:"last_check"`         // 最后检查时间
}

// 通知相关类型
type NotificationOptions struct {
	TemplateID string
	Target     NotificationTarget
	Variables  map[string]interface{}
	GuildID    string
	Priority   NotificationPriority
	Metadata   map[string]interface{}
}

type NotificationTarget struct {
	Type string // "channel", "user", "webhook"
	ID   string
}

type NotificationPriority int

const (
	PriorityLow NotificationPriority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
)

type NotificationResult struct {
	ID           string
	Status       string
	MessageID    string
	Success      bool
	Error        error
	Timestamp    time.Time
	RenderTime   time.Duration
	DeliveryTime time.Duration
}

// Template 模板结构
type Template struct {
	ID          string                 `json:"templateId"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Category    string                 `json:"category"`
	Version     string                 `json:"version"`
	IsActive    bool                   `json:"isActive"`
	GuildID     *string                `json:"guildId"`
	AccessLevel string                 `json:"accessLevel"`
	Content     interface{}            `json:"content"`
	Variables   map[string]TemplateVar `json:"variables"`
	Metadata    TemplateMetadata       `json:"metadata"`
}

// TemplateVar 模板变量定义
type TemplateVar struct {
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"defaultValue,omitempty"`
}

// TemplateMetadata 模板元数据
type TemplateMetadata struct {
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
	CreatedBy  string    `json:"createdBy"`
	Author     string    `json:"author"`
	Version    string    `json:"version"`
	UsageCount int       `json:"usageCount"`
	Tags       []string  `json:"tags"`
	IsSystem   bool      `json:"isSystem"`
}

// TemplateIndex 模板索引结构
type TemplateIndex struct {
	Version     string              `json:"version"`
	LastUpdated string              `json:"lastUpdated"`
	Templates   []TemplateIndexItem `json:"templates"`
	Categories  []string            `json:"categories"`
}

// TemplateIndexItem 模板索引项
type TemplateIndexItem struct {
	TemplateID   string  `json:"templateId"`
	Name         string  `json:"name"`
	Category     string  `json:"category"`
	Type         string  `json:"type"`
	GuildID      *string `json:"guildId"`
	FilePath     string  `json:"filePath"`
	LastModified string  `json:"lastModified"`
}

type MessageMetadata struct {
	UserID    string
	GuildID   string
	ChannelID string
	Command   string
	Variables map[string]interface{}
}

type MessageStatus struct {
	MessageID string
	Status    string
	CreatedAt time.Time
	UpdatedAt time.Time
	Metadata  map[string]interface{}
}

type Trigger struct {
	ID        string
	Name      string
	EventType string
	Condition string
	Action    string
	Enabled   bool
	GuildID   string
}

type TriggerEvent struct {
	Type      string
	Data      map[string]interface{}
	Timestamp time.Time
	Source    string
}

// BatchMessage 批量消息
type BatchMessage struct {
	RoutingKey string         `json:"routing_key"`
	Body       []byte         `json:"body"`
	Options    PublishOptions `json:"options"`
}

// RetryHandler 重试处理器接口
type RetryHandler interface {
	Handle(body []byte, retryCount int64) error
}
