package commands

import (
	"context"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// ForwardCommand Forward命令处理器
// 实现/forward命令系列：add、remove、list、query
type ForwardCommand struct {
	name        string
	description string

	// 服务依赖
	forwardService types.ForwardRuleManager
	mappingService types.FieldMapper
}

// NewForwardCommand 创建Forward命令处理器
func NewForwardCommand(forwardService types.ForwardRuleManager, mappingService types.FieldMapper) *ForwardCommand {
	return &ForwardCommand{
		name:           "forward",
		description:    "管理转发规则：添加、删除、列出、查询转发规则",
		forwardService: forwardService,
		mappingService: mappingService,
	}
}

// GetName 获取命令名称
func (fc *ForwardCommand) GetName() string {
	return fc.name
}

// GetDescription 获取命令描述
func (fc *ForwardCommand) GetDescription() string {
	return fc.description
}

// GetCategory 获取命令分类
func (fc *ForwardCommand) GetCategory() string {
	return "管理"
}

// GetCooldown 获取冷却时间
func (fc *ForwardCommand) GetCooldown() time.Duration {
	return 5 * time.Second
}

// GetPermissions 获取所需权限
func (fc *ForwardCommand) GetPermissions() []string {
	return []string{"ADMINISTRATOR"} // 需要管理员权限
}

// Validate 验证命令参数
func (fc *ForwardCommand) Validate(interaction *discordgo.InteractionCreate) error {
	// 基本验证逻辑
	return nil
}

// Execute 执行命令
func (fc *ForwardCommand) Execute(ctx context.Context, client *types.Client, i *discordgo.InteractionCreate) error {
	s := client.Session
	// 权限检查由中间件处理，这里不需要额外检查

	// 检查服务依赖是否可用
	if fc.forwardService == nil {
		logger.Error("ForwardService 未初始化", "user", fc.getUserID(i))
		return fc.respondError(s, i, "转发服务暂时不可用，请稍后再试")
	}

	// 获取子命令
	options := i.ApplicationCommandData().Options
	if len(options) == 0 {
		return fc.respondError(s, i, "请指定子命令：add、remove、list、query")
	}

	subCommand := options[0].Name
	subOptions := options[0].Options

	switch subCommand {
	case "add":
		return fc.handleAdd(s, i, subOptions)
	case "remove":
		return fc.handleRemove(s, i, subOptions)
	case "list":
		return fc.handleList(s, i, subOptions)
	case "query":
		return fc.handleQuery(s, i, subOptions)
	default:
		return fc.respondError(s, i, fmt.Sprintf("未知的子命令: %s", subCommand))
	}
}

// handleAdd 处理添加转发规则
func (fc *ForwardCommand) handleAdd(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 添加 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			logger.Error("handleAdd panic recovered", "error", r, "user", fc.getUserID(i))
			fc.respondError(s, i, fmt.Sprintf("处理命令时发生内部错误: %v", r))
		}
	}()

	// 解析参数
	var sourceChannelID, targetChannelID, mappingGroup, ruleName string

	for _, option := range options {
		switch option.Name {
		case "source_channel":
			if channel, err := fc.getChannelFromOption(option); err != nil {
				logger.Error("获取源频道失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取源频道失败: %v", err))
			} else {
				sourceChannelID = channel.ID
			}
		case "target_channel":
			if channel, err := fc.getChannelFromOption(option); err != nil {
				logger.Error("获取目标频道失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取目标频道失败: %v", err))
			} else {
				targetChannelID = channel.ID
			}
		case "mapping_group":
			if value, err := fc.getStringFromOption(option); err != nil {
				logger.Error("获取映射组失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取映射组失败: %v", err))
			} else {
				mappingGroup = value
			}
		case "name":
			if value, err := fc.getStringFromOption(option); err != nil {
				logger.Error("获取规则名称失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取规则名称失败: %v", err))
			} else {
				ruleName = value
			}
		}
	}

	// 验证必需参数
	if sourceChannelID == "" || targetChannelID == "" {
		return fc.respondError(s, i, "源频道和目标频道是必需的")
	}

	// 生成规则名称（如果未提供）
	if ruleName == "" {
		ruleName = types.GenerateAutoName(sourceChannelID, targetChannelID)
	}

	// 设置默认映射组
	if mappingGroup == "" {
		mappingGroup = "generic"
	}

	// 创建转发规则
	rule := &types.ForwardRule{
		Name:              ruleName,
		Description:       fmt.Sprintf("从 <#%s> 转发到 <#%s>", sourceChannelID, targetChannelID),
		Enabled:           true,
		InputChannel:      sourceChannelID, // 使用新字段名
		OutputChannel:     targetChannelID, // 使用新字段名
		FieldMappingGroup: mappingGroup,
		ForwardConfig: types.ForwardConfig{
			Mode:                   "transform",
			DelaySeconds:           1,
			PreserveOriginalEmbeds: true,
			AddSourceInfo:          true,
		},
		FilterConfig: types.FilterConfig{
			Enabled: false,
		},
		Metadata: types.ForwardRuleMetadata{
			CreatedBy: fc.getUserID(i),
			Tags:      []string{"manual", "command"},
		},
	}

	// 添加规则
	if err := fc.forwardService.AddRule(rule); err != nil {
		logger.Error("添加转发规则失败", "error", err, "user", fc.getUserID(i))
		return fc.respondError(s, i, fmt.Sprintf("添加转发规则失败: %v", err))
	}

	// 记录操作日志
	fc.logOperation("add_forward_rule", fc.getUserID(i), map[string]interface{}{
		"rule_name":      ruleName,
		"source_channel": sourceChannelID,
		"target_channel": targetChannelID,
		"mapping_group":  mappingGroup,
	})

	// 响应成功
	embed := &discordgo.MessageEmbed{
		Title:       "✅ 转发规则已添加",
		Description: fmt.Sprintf("规则名称: `%s`\n源频道: <#%s>\n目标频道: <#%s>\n映射组: `%s`", ruleName, sourceChannelID, targetChannelID, mappingGroup),
		Color:       0x00ff00,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleRemove 处理删除转发规则
func (fc *ForwardCommand) handleRemove(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 添加 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			logger.Error("handleRemove panic recovered", "error", r, "user", fc.getUserID(i))
			fc.respondError(s, i, fmt.Sprintf("处理删除命令时发生内部错误: %v", r))
		}
	}()

	// 解析参数
	var ruleName string
	for _, option := range options {
		if option.Name == "name" {
			if value, err := fc.getStringFromOption(option); err != nil {
				logger.Error("获取规则名称失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取规则名称失败: %v", err))
			} else {
				ruleName = value
			}
			break
		}
	}

	if ruleName == "" {
		return fc.respondError(s, i, "规则名称是必需的")
	}

	// 检查规则是否存在
	rule, err := fc.forwardService.GetRule(ruleName)
	if err != nil {
		return fc.respondError(s, i, fmt.Sprintf("规则不存在: %s", ruleName))
	}

	// 删除规则
	if err := fc.forwardService.RemoveRule(ruleName); err != nil {
		logger.Error("删除转发规则失败", "error", err, "user", fc.getUserID(i))
		return fc.respondError(s, i, fmt.Sprintf("删除转发规则失败: %v", err))
	}

	// 记录操作日志
	fc.logOperation("remove_forward_rule", fc.getUserID(i), map[string]interface{}{
		"rule_name":      ruleName,
		"source_channel": rule.GetSourceChannelID(),
		"target_channel": rule.GetTargetChannelID(),
	})

	// 响应成功
	embed := &discordgo.MessageEmbed{
		Title:       "✅ 转发规则已删除",
		Description: fmt.Sprintf("规则 `%s` 已成功删除", ruleName),
		Color:       0x00ff00,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleList 处理列出转发规则
func (fc *ForwardCommand) handleList(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 添加 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			logger.Error("handleList panic recovered", "error", r, "user", fc.getUserID(i))
			fc.respondError(s, i, fmt.Sprintf("处理列表命令时发生内部错误: %v", r))
		}
	}()

	// 获取所有规则
	rules := fc.forwardService.ListRules()

	if len(rules) == 0 {
		embed := &discordgo.MessageEmbed{
			Title:       "📋 转发规则列表",
			Description: "当前没有配置任何转发规则",
			Color:       0xffff00,
		}

		return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseChannelMessageWithSource,
			Data: &discordgo.InteractionResponseData{
				Embeds: []*discordgo.MessageEmbed{embed},
			},
		})
	}

	// 构建规则列表
	var description strings.Builder
	description.WriteString(fmt.Sprintf("共找到 %d 个转发规则:\n\n", len(rules)))

	for i, rule := range rules {
		if i >= 10 { // 限制显示数量
			description.WriteString(fmt.Sprintf("... 还有 %d 个规则\n", len(rules)-10))
			break
		}

		status := "🔴 禁用"
		if rule.Enabled {
			status = "🟢 启用"
		}

		description.WriteString(fmt.Sprintf("**%d. %s** %s\n", i+1, rule.Name, status))
		description.WriteString(fmt.Sprintf("   源频道: <#%s> → 目标频道: <#%s>\n", rule.GetSourceChannelID(), rule.GetTargetChannelID()))
		description.WriteString(fmt.Sprintf("   映射组: `%s`\n\n", rule.FieldMappingGroup))
	}

	embed := &discordgo.MessageEmbed{
		Title:       "📋 转发规则列表",
		Description: description.String(),
		Color:       0x0099ff,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleQuery 处理查询转发规则
func (fc *ForwardCommand) handleQuery(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 添加 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			logger.Error("handleQuery panic recovered", "error", r, "user", fc.getUserID(i))
			fc.respondError(s, i, fmt.Sprintf("处理查询命令时发生内部错误: %v", r))
		}
	}()

	// 解析参数
	var ruleName, channelID string
	for _, option := range options {
		switch option.Name {
		case "name":
			if value, err := fc.getStringFromOption(option); err != nil {
				logger.Error("获取规则名称失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取规则名称失败: %v", err))
			} else {
				ruleName = value
			}
		case "channel":
			if channel, err := fc.getChannelFromOption(option); err != nil {
				logger.Error("获取频道失败", "error", err, "option_type", option.Type, "user", fc.getUserID(i))
				return fc.respondError(s, i, fmt.Sprintf("获取频道失败: %v", err))
			} else {
				channelID = channel.ID
			}
		}
	}

	if ruleName != "" {
		// 查询特定规则
		return fc.queryRuleByName(s, i, ruleName)
	} else if channelID != "" {
		// 查询频道相关规则
		return fc.queryRulesByChannel(s, i, channelID)
	} else {
		return fc.respondError(s, i, "请指定规则名称或频道ID")
	}
}

// queryRuleByName 根据名称查询规则
func (fc *ForwardCommand) queryRuleByName(s *discordgo.Session, i *discordgo.InteractionCreate, ruleName string) error {
	rule, err := fc.forwardService.GetRule(ruleName)
	if err != nil {
		return fc.respondError(s, i, fmt.Sprintf("规则不存在: %s", ruleName))
	}

	// 获取统计信息
	stats, _ := fc.forwardService.GetRuleStats(ruleName)

	// 构建详细信息
	embed := &discordgo.MessageEmbed{
		Title: fmt.Sprintf("🔍 转发规则详情: %s", rule.Name),
		Color: 0x0099ff,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "状态",
				Value:  fc.getStatusText(rule.Enabled),
				Inline: true,
			},
			{
				Name:   "源频道",
				Value:  fmt.Sprintf("<#%s>", rule.GetSourceChannelID()),
				Inline: true,
			},
			{
				Name:   "目标频道",
				Value:  fmt.Sprintf("<#%s>", rule.GetTargetChannelID()),
				Inline: true,
			},
			{
				Name:   "映射组",
				Value:  rule.FieldMappingGroup,
				Inline: true,
			},
			{
				Name:   "转发模式",
				Value:  rule.ForwardConfig.Mode,
				Inline: true,
			},
			{
				Name:   "延迟时间",
				Value:  fmt.Sprintf("%d秒", rule.ForwardConfig.DelaySeconds),
				Inline: true,
			},
		},
	}

	if rule.Description != "" {
		embed.Description = rule.Description
	}

	// 添加统计信息
	if stats != nil {
		embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
			Name:   "统计信息",
			Value:  fmt.Sprintf("总消息: %d\n转发: %d\n过滤: %d\n错误: %d", stats.TotalMessages, stats.ForwardedMessages, stats.FilteredMessages, stats.ErrorMessages),
			Inline: false,
		})
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// queryRulesByChannel 根据频道查询规则
func (fc *ForwardCommand) queryRulesByChannel(s *discordgo.Session, i *discordgo.InteractionCreate, channelID string) error {
	// 获取源频道规则
	sourceRules := fc.forwardService.GetRulesBySourceChannel(channelID)
	targetRules := fc.forwardService.GetRulesByTargetChannel(channelID)

	if len(sourceRules) == 0 && len(targetRules) == 0 {
		embed := &discordgo.MessageEmbed{
			Title:       "🔍 频道转发规则查询",
			Description: fmt.Sprintf("频道 <#%s> 没有相关的转发规则", channelID),
			Color:       0xffff00,
		}

		return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseChannelMessageWithSource,
			Data: &discordgo.InteractionResponseData{
				Embeds: []*discordgo.MessageEmbed{embed},
			},
		})
	}

	var description strings.Builder
	description.WriteString(fmt.Sprintf("频道 <#%s> 的转发规则:\n\n", channelID))

	if len(sourceRules) > 0 {
		description.WriteString("**作为源频道的规则:**\n")
		for _, rule := range sourceRules {
			status := "🔴"
			if rule.Enabled {
				status = "🟢"
			}
			description.WriteString(fmt.Sprintf("%s `%s` → <#%s>\n", status, rule.Name, rule.GetTargetChannelID()))
		}
		description.WriteString("\n")
	}

	if len(targetRules) > 0 {
		description.WriteString("**作为目标频道的规则:**\n")
		for _, rule := range targetRules {
			status := "🔴"
			if rule.Enabled {
				status = "🟢"
			}
			description.WriteString(fmt.Sprintf("%s `%s` ← <#%s>\n", status, rule.Name, rule.GetSourceChannelID()))
		}
	}

	embed := &discordgo.MessageEmbed{
		Title:       "🔍 频道转发规则查询",
		Description: description.String(),
		Color:       0x0099ff,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// 辅助方法

// hasPermission 检查用户权限 - 已废弃，权限检查由中间件处理
// func (fc *ForwardCommand) hasPermission(i *discordgo.InteractionCreate) bool {
//     // 权限检查现在由PermissionManager和中间件处理
//     return true
// }

// getUserID 获取用户ID
func (fc *ForwardCommand) getUserID(i *discordgo.InteractionCreate) string {
	if i.Member != nil {
		return i.Member.User.ID
	}
	if i.User != nil {
		return i.User.ID
	}
	return "unknown"
}

// getStatusText 获取状态文本
func (fc *ForwardCommand) getStatusText(enabled bool) string {
	if enabled {
		return "🟢 启用"
	}
	return "🔴 禁用"
}

// respondError 响应错误信息
func (fc *ForwardCommand) respondError(s *discordgo.Session, i *discordgo.InteractionCreate, message string) error {
	embed := &discordgo.MessageEmbed{
		Title:       "❌ 错误",
		Description: message,
		Color:       0xff0000,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
			Flags:  discordgo.MessageFlagsEphemeral,
		},
	})
}

// logOperation 记录操作日志
func (fc *ForwardCommand) logOperation(operation, userID string, data map[string]interface{}) {
	logger.Info("转发规则操作",
		"operation", operation,
		"user_id", userID,
		"data", data,
	)
}

// getChannelFromOption 安全地从选项中获取频道信息
func (fc *ForwardCommand) getChannelFromOption(option *discordgo.ApplicationCommandInteractionDataOption) (*discordgo.Channel, error) {
	if option.Type != discordgo.ApplicationCommandOptionChannel {
		return nil, fmt.Errorf("选项类型不匹配，期望 Channel，实际 %v", option.Type)
	}

	channel := option.ChannelValue(nil)
	if channel == nil {
		return nil, fmt.Errorf("无法获取频道信息")
	}

	return channel, nil
}

// getStringFromOption 安全地从选项中获取字符串值
func (fc *ForwardCommand) getStringFromOption(option *discordgo.ApplicationCommandInteractionDataOption) (string, error) {
	if option.Type != discordgo.ApplicationCommandOptionString {
		return "", fmt.Errorf("选项类型不匹配，期望 String，实际 %v", option.Type)
	}

	return option.StringValue(), nil
}

// GetApplicationCommand 获取应用命令定义
func (fc *ForwardCommand) GetApplicationCommand() *discordgo.ApplicationCommand {
	return &discordgo.ApplicationCommand{
		Name:        fc.name,
		Description: fc.description,
		Options: []*discordgo.ApplicationCommandOption{
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "add",
				Description: "添加转发规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "source_channel",
						Description: "源频道",
						Required:    true,
					},
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "target_channel",
						Description: "目标频道",
						Required:    true,
					},
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "mapping_group",
						Description: "字段映射组",
						Required:    false,
						Choices: []*discordgo.ApplicationCommandOptionChoice{
							{Name: "Amazon标准", Value: "amazon_standard"},
							{Name: "eBay标准", Value: "ebay_standard"},
							{Name: "AliExpress标准", Value: "aliexpress_standard"},
							{Name: "通用", Value: "generic"},
						},
					},
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "name",
						Description: "规则名称（可选，自动生成）",
						Required:    false,
					},
				},
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "remove",
				Description: "删除转发规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "name",
						Description: "规则名称",
						Required:    true,
					},
				},
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "list",
				Description: "列出所有转发规则",
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "query",
				Description: "查询转发规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "name",
						Description: "规则名称",
						Required:    false,
					},
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "channel",
						Description: "频道ID",
						Required:    false,
					},
				},
			},
		},
	}
}
