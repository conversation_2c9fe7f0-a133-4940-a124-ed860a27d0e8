package commands

import (
	"testing"

	"github.com/bwmarrin/discordgo"
)

// TestGetChannelFromOption 测试频道选项处理
func TestGetChannelFromOption(t *testing.T) {
	fc := &ForwardCommand{}

	// 测试正确的频道选项
	channelOption := &discordgo.ApplicationCommandInteractionDataOption{
		Name: "test_channel",
		Type: discordgo.ApplicationCommandOptionChannel,
		Value: &discordgo.Channel{
			ID:   "123456789",
			Name: "test-channel",
		},
	}

	channel, err := fc.getChannelFromOption(channelOption)
	if err != nil {
		t.Errorf("期望成功获取频道，但得到错误: %v", err)
	}
	if channel == nil {
		t.Error("期望获取到频道对象，但得到 nil")
	}

	// 测试错误的选项类型（这应该会导致原始 panic 的情况）
	stringOption := &discordgo.ApplicationCommandInteractionDataOption{
		Name:  "test_string",
		Type:  discordgo.ApplicationCommandOptionString,
		Value: "test_value",
	}

	_, err = fc.getChannelFromOption(stringOption)
	if err == nil {
		t.Error("期望得到类型不匹配错误，但没有错误")
	}
}

// TestGetStringFromOption 测试字符串选项处理
func TestGetStringFromOption(t *testing.T) {
	fc := &ForwardCommand{}

	// 测试正确的字符串选项
	stringOption := &discordgo.ApplicationCommandInteractionDataOption{
		Name:  "test_string",
		Type:  discordgo.ApplicationCommandOptionString,
		Value: "test_value",
	}

	value, err := fc.getStringFromOption(stringOption)
	if err != nil {
		t.Errorf("期望成功获取字符串，但得到错误: %v", err)
	}
	if value != "test_value" {
		t.Errorf("期望得到 'test_value'，但得到 '%s'", value)
	}

	// 测试错误的选项类型
	channelOption := &discordgo.ApplicationCommandInteractionDataOption{
		Name: "test_channel",
		Type: discordgo.ApplicationCommandOptionChannel,
		Value: &discordgo.Channel{
			ID:   "123456789",
			Name: "test-channel",
		},
	}

	_, err = fc.getStringFromOption(channelOption)
	if err == nil {
		t.Error("期望得到类型不匹配错误，但没有错误")
	}
}

// TestOptionTypeSafety 测试选项类型安全性
func TestOptionTypeSafety(t *testing.T) {
	fc := &ForwardCommand{}

	// 模拟原始 panic 情况：尝试在 Channel 类型上调用 StringValue
	channelOption := &discordgo.ApplicationCommandInteractionDataOption{
		Name: "source_channel",
		Type: discordgo.ApplicationCommandOptionChannel,
		Value: &discordgo.Channel{
			ID:   "123456789",
			Name: "test-channel",
		},
	}

	// 这应该安全地返回错误而不是 panic
	_, err := fc.getStringFromOption(channelOption)
	if err == nil {
		t.Error("期望得到类型不匹配错误")
	}

	// 验证错误消息包含类型信息
	expectedError := "选项类型不匹配，期望 String，实际 Channel"
	if err.Error() != expectedError {
		t.Errorf("期望错误消息 '%s'，但得到 '%s'", expectedError, err.Error())
	}
}
