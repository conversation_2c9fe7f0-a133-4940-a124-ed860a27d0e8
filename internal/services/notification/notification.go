package notification

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// NotificationService 通知服务
type NotificationService struct {
	session   *discordgo.Session
	config    *types.Config
	cache     types.CacheService
	queue     types.QueueService
	templates map[string]*NotificationTemplate
	triggers  map[string]*NotificationTrigger
	mu        sync.RWMutex
	stats     *NotificationStats
	statsMu   sync.RWMutex
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        NotificationType       `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Color       int                    `json:"color"`
	Fields      []NotificationField    `json:"fields"`
	Footer      string                 `json:"footer"`
	Thumbnail   string                 `json:"thumbnail"`
	Image       string                 `json:"image"`
	Variables   map[string]interface{} `json:"variables"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// NotificationField 通知字段
type NotificationField struct {
	Name   string `json:"name"`
	Value  string `json:"value"`
	Inline bool   `json:"inline"`
}

// NotificationType 通知类型
type NotificationType string

const (
	NotificationTypeInfo    NotificationType = "info"
	NotificationTypeSuccess NotificationType = "success"
	NotificationTypeWarning NotificationType = "warning"
	NotificationTypeError   NotificationType = "error"
	NotificationTypeCustom  NotificationType = "custom"
)

// NotificationTrigger 通知触发器
type NotificationTrigger struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Event         string                 `json:"event"`
	Conditions    []TriggerCondition     `json:"conditions"`
	Actions       []TriggerAction        `json:"actions"`
	Enabled       bool                   `json:"enabled"`
	Cooldown      time.Duration          `json:"cooldown"`
	LastTriggered time.Time              `json:"last_triggered"`
	Variables     map[string]interface{} `json:"variables"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// TriggerCondition 触发条件
type TriggerCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// TriggerAction 触发动作
type TriggerAction struct {
	Type      string                 `json:"type"`
	Target    string                 `json:"target"`
	Template  string                 `json:"template"`
	Variables map[string]interface{} `json:"variables"`
	Delay     time.Duration          `json:"delay"`
}

// NotificationStats 通知统计
type NotificationStats struct {
	TotalSent     int64     `json:"total_sent"`
	TotalFailed   int64     `json:"total_failed"`
	TemplatesUsed int64     `json:"templates_used"`
	TriggersRun   int64     `json:"triggers_run"`
	LastSent      time.Time `json:"last_sent"`
	LastUpdated   time.Time `json:"last_updated"`
}

// NewNotificationService 创建通知服务
func NewNotificationService(session *discordgo.Session, config *types.Config, cache types.CacheService, queue types.QueueService) *NotificationService {
	return &NotificationService{
		session:   session,
		config:    config,
		cache:     cache,
		queue:     queue,
		templates: make(map[string]*NotificationTemplate),
		triggers:  make(map[string]*NotificationTrigger),
		stats: &NotificationStats{
			LastUpdated: time.Now(),
		},
	}
}

// Initialize 初始化通知服务
func (ns *NotificationService) Initialize(ctx context.Context) error {
	logger.Info("初始化通知服务")

	// 加载默认模板
	if err := ns.loadDefaultTemplates(); err != nil {
		return fmt.Errorf("加载默认模板失败: %w", err)
	}

	// 加载默认触发器
	if err := ns.loadDefaultTriggers(); err != nil {
		return fmt.Errorf("加载默认触发器失败: %w", err)
	}

	logger.Info("通知服务初始化完成",
		"templates", len(ns.templates),
		"triggers", len(ns.triggers))

	return nil
}

// loadDefaultTemplates 加载默认模板
func (ns *NotificationService) loadDefaultTemplates() error {
	defaultTemplates := []*NotificationTemplate{
		{
			ID:          "welcome",
			Name:        "欢迎消息",
			Type:        NotificationTypeSuccess,
			Title:       "🎉 欢迎加入！",
			Description: "欢迎 {{.user}} 加入我们的社区！",
			Color:       0x00ff00,
			Fields: []NotificationField{
				{
					Name:   "👤 用户",
					Value:  "{{.user}}",
					Inline: true,
				},
				{
					Name:   "📅 加入时间",
					Value:  "{{.join_time}}",
					Inline: true,
				},
			},
			Footer:    "欢迎来到我们的服务器！",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "goodbye",
			Name:        "告别消息",
			Type:        NotificationTypeWarning,
			Title:       "👋 再见！",
			Description: "{{.user}} 离开了服务器",
			Color:       0xff9900,
			Fields: []NotificationField{
				{
					Name:   "👤 用户",
					Value:  "{{.user}}",
					Inline: true,
				},
				{
					Name:   "📅 离开时间",
					Value:  "{{.leave_time}}",
					Inline: true,
				},
			},
			Footer:    "希望你能再次回来！",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "moderation",
			Name:        "管理操作",
			Type:        NotificationTypeError,
			Title:       "🔨 管理操作",
			Description: "执行了管理操作：{{.action}}",
			Color:       0xff0000,
			Fields: []NotificationField{
				{
					Name:   "👮 执行者",
					Value:  "{{.moderator}}",
					Inline: true,
				},
				{
					Name:   "👤 目标用户",
					Value:  "{{.target}}",
					Inline: true,
				},
				{
					Name:   "📝 原因",
					Value:  "{{.reason}}",
					Inline: false,
				},
			},
			Footer:    "管理日志",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "system_alert",
			Name:        "系统警报",
			Type:        NotificationTypeError,
			Title:       "⚠️ 系统警报",
			Description: "{{.message}}",
			Color:       0xff0000,
			Fields: []NotificationField{
				{
					Name:   "🕐 时间",
					Value:  "{{.timestamp}}",
					Inline: true,
				},
				{
					Name:   "📊 严重程度",
					Value:  "{{.severity}}",
					Inline: true,
				},
			},
			Footer:    "系统监控",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, template := range defaultTemplates {
		ns.templates[template.ID] = template
	}

	return nil
}

// loadDefaultTriggers 加载默认触发器
func (ns *NotificationService) loadDefaultTriggers() error {
	defaultTriggers := []*NotificationTrigger{
		{
			ID:      "member_join",
			Name:    "成员加入触发器",
			Event:   "GUILD_MEMBER_ADD",
			Enabled: true,
			Actions: []TriggerAction{
				{
					Type:     "send_notification",
					Target:   "welcome_channel",
					Template: "welcome",
				},
			},
			Cooldown:  0,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:      "member_leave",
			Name:    "成员离开触发器",
			Event:   "GUILD_MEMBER_REMOVE",
			Enabled: true,
			Actions: []TriggerAction{
				{
					Type:     "send_notification",
					Target:   "goodbye_channel",
					Template: "goodbye",
				},
			},
			Cooldown:  0,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, trigger := range defaultTriggers {
		ns.triggers[trigger.ID] = trigger
	}

	return nil
}

// SendNotification 发送通知
func (ns *NotificationService) SendNotification(ctx context.Context, templateID, channelID string, variables map[string]interface{}) error {
	ns.mu.RLock()
	template, exists := ns.templates[templateID]
	ns.mu.RUnlock()

	if !exists {
		return fmt.Errorf("模板 %s 不存在", templateID)
	}

	// 渲染模板
	embed, err := ns.renderTemplate(template, variables)
	if err != nil {
		return fmt.Errorf("渲染模板失败: %w", err)
	}

	// 发送消息
	_, err = ns.session.ChannelMessageSendEmbed(channelID, embed)
	if err != nil {
		ns.updateStats(false)
		return fmt.Errorf("发送消息失败: %w", err)
	}

	ns.updateStats(true)

	logger.Info("通知发送成功",
		"template", templateID,
		"channel", channelID)

	return nil
}

// renderTemplate 渲染模板
func (ns *NotificationService) renderTemplate(template *NotificationTemplate, variables map[string]interface{}) (*discordgo.MessageEmbed, error) {
	// 简单的变量替换实现
	// 在实际项目中，可以使用更强大的模板引擎如 text/template

	embed := &discordgo.MessageEmbed{
		Title:       ns.replaceVariables(template.Title, variables),
		Description: ns.replaceVariables(template.Description, variables),
		Color:       template.Color,
		Timestamp:   time.Now().Format(time.RFC3339),
	}

	// 处理字段
	for _, field := range template.Fields {
		embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
			Name:   ns.replaceVariables(field.Name, variables),
			Value:  ns.replaceVariables(field.Value, variables),
			Inline: field.Inline,
		})
	}

	// 处理页脚
	if template.Footer != "" {
		embed.Footer = &discordgo.MessageEmbedFooter{
			Text: ns.replaceVariables(template.Footer, variables),
		}
	}

	// 处理缩略图
	if template.Thumbnail != "" {
		embed.Thumbnail = &discordgo.MessageEmbedThumbnail{
			URL: ns.replaceVariables(template.Thumbnail, variables),
		}
	}

	// 处理图片
	if template.Image != "" {
		embed.Image = &discordgo.MessageEmbedImage{
			URL: ns.replaceVariables(template.Image, variables),
		}
	}

	return embed, nil
}

// replaceVariables 替换变量
func (ns *NotificationService) replaceVariables(text string, variables map[string]interface{}) string {
	// 简单的变量替换实现
	// 在实际项目中，应该使用更强大的模板引擎
	result := text

	for key, value := range variables {
		placeholder := fmt.Sprintf("{{.%s}}", key)
		replacement := fmt.Sprintf("%v", value)
		// 简单的字符串替换
		if placeholder != "" && replacement != "" {
			// 这里应该实现真正的字符串替换
			// 暂时返回原文本
		}
	}

	return result
}

// ProcessTrigger 处理触发器
func (ns *NotificationService) ProcessTrigger(ctx context.Context, event string, data map[string]interface{}) error {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	for _, trigger := range ns.triggers {
		if !trigger.Enabled || trigger.Event != event {
			continue
		}

		// 检查冷却时间
		if trigger.Cooldown > 0 && time.Since(trigger.LastTriggered) < trigger.Cooldown {
			continue
		}

		// 检查条件
		if !ns.checkConditions(trigger.Conditions, data) {
			continue
		}

		// 执行动作
		if err := ns.executeActions(ctx, trigger.Actions, data); err != nil {
			logger.Error("执行触发器动作失败",
				"trigger", trigger.ID,
				"error", err)
			continue
		}

		// 更新最后触发时间
		trigger.LastTriggered = time.Now()

		ns.statsMu.Lock()
		ns.stats.TriggersRun++
		ns.stats.LastUpdated = time.Now()
		ns.statsMu.Unlock()

		logger.Debug("触发器执行成功", "trigger", trigger.ID, "event", event)
	}

	return nil
}

// checkConditions 检查条件
func (ns *NotificationService) checkConditions(conditions []TriggerCondition, data map[string]interface{}) bool {
	for _, condition := range conditions {
		value, exists := data[condition.Field]
		if !exists {
			return false
		}

		// 简单的条件检查实现
		switch condition.Operator {
		case "equals":
			if value != condition.Value {
				return false
			}
		case "not_equals":
			if value == condition.Value {
				return false
			}
		case "contains":
			if str, ok := value.(string); ok {
				if target, ok := condition.Value.(string); ok {
					// 简单的包含检查
					if len(str) > 0 && len(target) > 0 {
						// 这里应该实现真正的包含检查
						// 暂时总是返回 true
					}
				}
			}
		}
	}

	return true
}

// executeActions 执行动作
func (ns *NotificationService) executeActions(ctx context.Context, actions []TriggerAction, data map[string]interface{}) error {
	for _, action := range actions {
		switch action.Type {
		case "send_notification":
			channelID := ns.getChannelID(action.Target)
			if channelID == "" {
				continue
			}

			variables := make(map[string]interface{})
			for k, v := range data {
				variables[k] = v
			}
			for k, v := range action.Variables {
				variables[k] = v
			}

			if action.Delay > 0 {
				// 延迟执行
				go func() {
					time.Sleep(action.Delay)
					ns.SendNotification(ctx, action.Template, channelID, variables)
				}()
			} else {
				if err := ns.SendNotification(ctx, action.Template, channelID, variables); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// getChannelID 获取频道ID
func (ns *NotificationService) getChannelID(target string) string {
	// 这里应该从配置或数据库中获取频道ID
	// 暂时返回空字符串
	return ""
}

// updateStats 更新统计信息
func (ns *NotificationService) updateStats(success bool) {
	ns.statsMu.Lock()
	defer ns.statsMu.Unlock()

	if success {
		ns.stats.TotalSent++
		ns.stats.LastSent = time.Now()
	} else {
		ns.stats.TotalFailed++
	}

	ns.stats.LastUpdated = time.Now()
}

// GetStats 获取统计信息
func (ns *NotificationService) GetStats() *NotificationStats {
	ns.statsMu.RLock()
	defer ns.statsMu.RUnlock()

	// 返回副本
	stats := *ns.stats
	return &stats
}

// AddTemplate 添加模板
func (ns *NotificationService) AddTemplate(template *NotificationTemplate) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	if _, exists := ns.templates[template.ID]; exists {
		return fmt.Errorf("模板 %s 已存在", template.ID)
	}

	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()
	ns.templates[template.ID] = template

	logger.Info("添加通知模板", "id", template.ID, "name", template.Name)
	return nil
}

// GetTemplate 获取模板
func (ns *NotificationService) GetTemplate(id string) (*NotificationTemplate, bool) {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	template, exists := ns.templates[id]
	return template, exists
}

// AddTrigger 添加触发器
func (ns *NotificationService) AddTrigger(trigger *NotificationTrigger) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	if _, exists := ns.triggers[trigger.ID]; exists {
		return fmt.Errorf("触发器 %s 已存在", trigger.ID)
	}

	trigger.CreatedAt = time.Now()
	trigger.UpdatedAt = time.Now()
	ns.triggers[trigger.ID] = trigger

	logger.Info("添加通知触发器", "id", trigger.ID, "name", trigger.Name)
	return nil
}

// GetTrigger 获取触发器
func (ns *NotificationService) GetTrigger(id string) (*NotificationTrigger, bool) {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	trigger, exists := ns.triggers[id]
	return trigger, exists
}
