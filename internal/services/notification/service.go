package notification

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/template"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// Service 新的通知服务实现
type Service struct {
	session          *discordgo.Session
	config           *types.Config
	cache            types.CacheService
	queue            types.QueueService
	templateManager  *template.Manager
	templateRenderer *template.Renderer
	messageTracker   map[string]*MessageTracker
	triggers         map[string]*Trigger
	mu               sync.RWMutex
	stats            *Stats
	statsMu          sync.RWMutex
}

// MessageTracker 消息跟踪器
type MessageTracker struct {
	MessageID   string                 `json:"message_id"`
	TemplateID  string                 `json:"template_id"`
	ChannelID   string                 `json:"channel_id"`
	GuildID     string                 `json:"guild_id"`
	UserID      string                 `json:"user_id"`
	Status      string                 `json:"status"`
	Variables   map[string]interface{} `json:"variables"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DeliveredAt *time.Time             `json:"delivered_at"`
	FailedAt    *time.Time             `json:"failed_at"`
	Error       string                 `json:"error,omitempty"`
}

// Trigger 触发器
type Trigger struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	EventType   string                 `json:"event_type"`
	Conditions  []TriggerCondition     `json:"conditions"`
	Actions     []TriggerAction        `json:"actions"`
	Enabled     bool                   `json:"enabled"`
	Cooldown    time.Duration          `json:"cooldown"`
	Variables   map[string]interface{} `json:"variables"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// 使用 notification.go 中已定义的类型

// Stats 统计信息
type Stats struct {
	TotalSent     int64            `json:"total_sent"`
	TotalFailed   int64            `json:"total_failed"`
	LastSent      time.Time        `json:"last_sent"`
	LastFailed    time.Time        `json:"last_failed"`
	TemplateUsage map[string]int64 `json:"template_usage"`
}

// NewService 创建新的通知服务
func NewService(session *discordgo.Session, config *types.Config, cache types.CacheService, queue types.QueueService) *Service {
	return &Service{
		session:          session,
		config:           config,
		cache:            cache,
		queue:            queue,
		templateManager:  template.NewManager("templates"),
		templateRenderer: template.NewRenderer(),
		messageTracker:   make(map[string]*MessageTracker),
		triggers:         make(map[string]*Trigger),
		stats: &Stats{
			TemplateUsage: make(map[string]int64),
		},
	}
}

// Initialize 初始化服务
func (s *Service) Initialize(ctx context.Context) error {
	logger.Info("📢 初始化通知服务...")

	// 初始化模板管理器
	if err := s.templateManager.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化模板管理器失败: %w", err)
	}

	// 加载触发器
	if err := s.loadTriggers(); err != nil {
		logger.Warn("加载触发器失败", "error", err)
	}

	logger.Info("✅ 通知服务初始化完成")
	return nil
}

// Shutdown 关闭服务
func (s *Service) Shutdown(ctx context.Context) error {
	logger.Info("📢 关闭通知服务...")
	return nil
}

// LoadTemplate 加载模板
func (s *Service) LoadTemplate(templateID string, guildID ...string) (*types.Template, error) {
	return s.templateManager.LoadTemplate(templateID, guildID...)
}

// ReloadTemplates 重新加载模板
func (s *Service) ReloadTemplates() error {
	return s.templateManager.ReloadTemplates()
}

// ValidateTemplate 验证模板
func (s *Service) ValidateTemplate(template *types.Template) error {
	return s.templateManager.ValidateTemplate(template)
}

// SendNotification 发送通知
func (s *Service) SendNotification(ctx context.Context, options types.NotificationOptions) (*types.NotificationResult, error) {
	startTime := time.Now()

	// 获取模板
	tmpl, err := s.templateManager.GetTemplate(options.TemplateID, options.GuildID)
	if err != nil {
		return nil, fmt.Errorf("获取模板失败: %w", err)
	}

	// 渲染模板
	renderCtx := template.RenderContext{
		Template:  tmpl,
		Variables: options.Variables,
		GuildID:   options.GuildID,
	}

	rendered, err := s.templateRenderer.Render(renderCtx)
	if err != nil {
		return nil, fmt.Errorf("渲染模板失败: %w", err)
	}

	// 发送消息
	var message *discordgo.Message
	if len(rendered.Embeds) > 0 {
		// 发送嵌入消息
		message, err = s.session.ChannelMessageSendComplex(options.Target.ID, &discordgo.MessageSend{
			Content:    rendered.Content,
			Embeds:     rendered.Embeds,
			Components: rendered.Components,
			Files:      rendered.Files,
		})
	} else {
		// 发送普通消息
		message, err = s.session.ChannelMessageSend(options.Target.ID, rendered.Content)
	}

	if err != nil {
		s.updateStats(false, options.TemplateID)
		return &types.NotificationResult{
			Success:   false,
			Status:    "failed",
			MessageID: "",
			Error:     err,
			Timestamp: time.Now(),
		}, err
	}

	// 更新统计
	s.updateStats(true, options.TemplateID)

	// 跟踪消息
	s.trackMessage(message.ID, options)

	result := &types.NotificationResult{
		Success:      true,
		Status:       "delivered",
		MessageID:    message.ID,
		Error:        nil,
		Timestamp:    time.Now(),
		RenderTime:   rendered.RenderingTime,
		DeliveryTime: time.Since(startTime),
	}

	logger.Info("📤 通知发送成功",
		"template_id", options.TemplateID,
		"message_id", message.ID,
		"channel_id", options.Target.ID,
		"render_time", rendered.RenderingTime,
		"delivery_time", result.DeliveryTime)

	return result, nil
}

// SendBulkNotifications 批量发送通知
func (s *Service) SendBulkNotifications(ctx context.Context, notifications []types.NotificationOptions) ([]*types.NotificationResult, error) {
	results := make([]*types.NotificationResult, len(notifications))

	for i, notification := range notifications {
		result, err := s.SendNotification(ctx, notification)
		if err != nil {
			result = &types.NotificationResult{
				Success:   false,
				Status:    "failed",
				MessageID: "",
				Error:     err,
				Timestamp: time.Now(),
			}
		}
		results[i] = result
	}

	return results, nil
}

// TrackMessage 跟踪消息
func (s *Service) TrackMessage(messageID string, metadata types.MessageMetadata) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	tracker := &MessageTracker{
		MessageID: messageID,
		ChannelID: metadata.ChannelID,
		GuildID:   metadata.GuildID,
		UserID:    metadata.UserID,
		Status:    "delivered",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	now := time.Now()
	tracker.DeliveredAt = &now

	s.messageTracker[messageID] = tracker
	return nil
}

// GetMessageStatus 获取消息状态
func (s *Service) GetMessageStatus(messageID string) (*types.MessageStatus, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	tracker, exists := s.messageTracker[messageID]
	if !exists {
		return nil, fmt.Errorf("消息 %s 不存在", messageID)
	}

	return &types.MessageStatus{
		MessageID: tracker.MessageID,
		Status:    tracker.Status,
		CreatedAt: tracker.CreatedAt,
		UpdatedAt: tracker.UpdatedAt,
	}, nil
}

// UpdateMessageStatus 更新消息状态
func (s *Service) UpdateMessageStatus(messageID string, status string, metadata map[string]interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	tracker, exists := s.messageTracker[messageID]
	if !exists {
		return fmt.Errorf("消息 %s 不存在", messageID)
	}

	tracker.Status = status
	tracker.UpdatedAt = time.Now()

	if status == "failed" {
		now := time.Now()
		tracker.FailedAt = &now
		if errorMsg, exists := metadata["error"]; exists {
			if errorStr, ok := errorMsg.(string); ok {
				tracker.Error = errorStr
			}
		}
	}

	return nil
}

// ProcessEvent 处理事件
func (s *Service) ProcessEvent(ctx context.Context, event *types.TriggerEvent) error {
	// 查找匹配的触发器
	for _, trigger := range s.triggers {
		if !trigger.Enabled {
			continue
		}

		if trigger.EventType == event.Type {
			// 检查条件
			if s.checkTriggerConditions(trigger, event.Data) {
				// 执行动作
				if err := s.executeTriggerActions(ctx, trigger, event.Data); err != nil {
					logger.Error("执行触发器动作失败",
						"trigger_id", trigger.ID,
						"event_type", event.Type,
						"error", err)
				}
			}
		}
	}

	return nil
}

// trackMessage 内部消息跟踪
func (s *Service) trackMessage(messageID string, options types.NotificationOptions) {
	s.mu.Lock()
	defer s.mu.Unlock()

	tracker := &MessageTracker{
		MessageID:  messageID,
		TemplateID: options.TemplateID,
		ChannelID:  options.Target.ID,
		GuildID:    options.GuildID,
		Status:     "delivered",
		Variables:  options.Variables,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	now := time.Now()
	tracker.DeliveredAt = &now

	s.messageTracker[messageID] = tracker
}

// updateStats 更新统计信息
func (s *Service) updateStats(success bool, templateID string) {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()

	if success {
		s.stats.TotalSent++
		s.stats.LastSent = time.Now()
		s.stats.TemplateUsage[templateID]++
	} else {
		s.stats.TotalFailed++
		s.stats.LastFailed = time.Now()
	}
}

// loadTriggers 加载触发器
func (s *Service) loadTriggers() error {
	// 这里可以从文件或数据库加载触发器
	// 暂时创建一些默认触发器
	return nil
}

// checkTriggerConditions 检查触发条件
func (s *Service) checkTriggerConditions(trigger *Trigger, data map[string]interface{}) bool {
	// 简化的条件检查实现
	return true
}

// executeTriggerActions 执行触发动作
func (s *Service) executeTriggerActions(ctx context.Context, trigger *Trigger, data map[string]interface{}) error {
	// 简化的动作执行实现
	return nil
}
