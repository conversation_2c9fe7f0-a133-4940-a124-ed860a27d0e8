package forward

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"gopkg.in/yaml.v3"
)

// ForwardRuleService 转发规则服务
// 实现 types.Service 接口，管理转发规则的加载、缓存和执行
type ForwardRuleService struct {
	// 服务基础信息
	name         string
	serviceType  string
	dependencies []string

	// 配置和状态
	configFile string
	config     *types.ForwardRulesConfig
	rules      map[string]*types.ForwardRule
	mu         sync.RWMutex

	// 运行状态
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc

	// 索引缓存
	sourceChannelIndex map[string][]*types.ForwardRule // 按源频道索引
	targetChannelIndex map[string][]*types.ForwardRule // 按目标频道索引

	// 统计和监控
	stats      map[string]*types.ForwardRuleStats
	lastLoaded time.Time

	// 服务依赖（运行时注入）
	queueService   types.QueueService
	mappingService types.FieldMapper
	filterService  types.FilterEngine

	// 全局设置应用
	rateLimiter  *RateLimiter
	errorHandler *ErrorHandler
	monitor      *ForwardMonitor

	// 频道名称缓存（遵循设计文档）
	channelCache ChannelNameCacheInterface
}

// NewForwardRuleService 创建转发规则服务
func NewForwardRuleService(configFile string) *ForwardRuleService {
	ctx, cancel := context.WithCancel(context.Background())

	return &ForwardRuleService{
		name:               "ForwardRuleService",
		serviceType:        "forward",
		dependencies:       []string{"FieldMappingService"}, // 依赖字段映射服务
		configFile:         configFile,
		rules:              make(map[string]*types.ForwardRule),
		sourceChannelIndex: make(map[string][]*types.ForwardRule),
		targetChannelIndex: make(map[string][]*types.ForwardRule),
		stats:              make(map[string]*types.ForwardRuleStats),
		ctx:                ctx,
		cancel:             cancel,
	}
}

// Initialize 初始化服务
func (frs *ForwardRuleService) Initialize(ctx context.Context) error {
	logger.Info("初始化转发规则服务", "config_file", frs.configFile)

	// 加载配置文件
	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 验证配置
	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 构建索引
	frs.buildIndexes()

	logger.Info("转发规则服务初始化完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// Start 启动服务
func (frs *ForwardRuleService) Start(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if frs.isRunning {
		return fmt.Errorf("转发规则服务已在运行")
	}

	// 设置上下文
	frs.ctx, frs.cancel = context.WithCancel(ctx)

	// 加载配置
	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 初始化全局设置组件
	if err := frs.initializeGlobalSettings(); err != nil {
		return fmt.Errorf("初始化全局设置失败: %w", err)
	}

	// 构建索引
	frs.buildIndexes()

	frs.isRunning = true
	logger.Info("✅ 转发规则服务已启动", "rules_count", len(frs.rules))

	return nil
}

// Stop 停止服务
func (frs *ForwardRuleService) Stop(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return nil
	}

	// 停止监控器
	if frs.monitor != nil {
		frs.monitor.Stop()
	}

	frs.cancel()
	frs.isRunning = false

	logger.Info("转发规则服务已停止")
	return nil
}

// HealthCheck 健康检查
func (frs *ForwardRuleService) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	startTime := time.Now()

	// 检查服务状态
	if !frs.isRunning {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "服务未运行",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(frs.configFile); os.IsNotExist(err) {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      fmt.Sprintf("配置文件不存在: %s", frs.configFile),
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	// 检查规则数量
	enabledRules := frs.countEnabledRules()
	if enabledRules == 0 {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "没有启用的转发规则",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	return &services.HealthCheckResult{
		Healthy:      true,
		Message:      fmt.Sprintf("服务正常，已加载 %d 个规则，其中 %d 个已启用", len(frs.rules), enabledRules),
		CheckTime:    startTime,
		ResponseTime: time.Since(startTime),
	}
}

// SetQueueService 设置队列服务（依赖注入）
func (frs *ForwardRuleService) SetQueueService(queueService types.QueueService) {
	frs.queueService = queueService
	logger.Debug("队列服务已注入到转发规则服务")
}

// SetMappingService 设置字段映射服务（依赖注入）
func (frs *ForwardRuleService) SetMappingService(mappingService types.FieldMapper) {
	frs.mappingService = mappingService
	logger.Debug("字段映射服务已注入到转发规则服务")
}

// SetFilterService 设置过滤服务（依赖注入）
func (frs *ForwardRuleService) SetFilterService(filterService types.FilterEngine) {
	frs.filterService = filterService
	logger.Debug("过滤服务已注入到转发规则服务")
}

// SetChannelCache 设置频道名称缓存（运行时注入）
func (frs *ForwardRuleService) SetChannelCache(channelCache ChannelNameCacheInterface) {
	frs.channelCache = channelCache
	logger.Debug("频道名称缓存已注入到转发规则服务")
}

// initializeGlobalSettings 初始化全局设置组件
func (frs *ForwardRuleService) initializeGlobalSettings() error {
	if frs.config == nil {
		logger.Warn("配置为空，跳过全局设置初始化")
		return nil
	}

	globalSettings := &frs.config.GlobalSettings

	// 初始化速率限制器
	if globalSettings.GlobalRateLimit != nil {
		frs.rateLimiter = NewRateLimiter(
			globalSettings.GlobalRateLimit.MaxMessages,
			globalSettings.GlobalRateLimit.TimeWindow)
		logger.Info("速率限制器已初始化",
			"max_messages", globalSettings.GlobalRateLimit.MaxMessages,
			"time_window", globalSettings.GlobalRateLimit.TimeWindow)
	}

	// 初始化错误处理器
	frs.errorHandler = NewErrorHandler(globalSettings.ErrorHandling)
	logger.Info("错误处理器已初始化",
		"max_retries", globalSettings.ErrorHandling.MaxRetries,
		"retry_delay", globalSettings.ErrorHandling.RetryDelay,
		"log_errors", globalSettings.ErrorHandling.LogErrors)

	// 初始化监控器
	frs.monitor = NewForwardMonitor(globalSettings.Monitoring)

	// 启动监控器
	if globalSettings.Monitoring.Enabled {
		frs.monitor.Start()
	}

	logger.Info("转发监控器已初始化",
		"enabled", globalSettings.Monitoring.Enabled,
		"stats_interval", globalSettings.Monitoring.StatsInterval,
		"health_check_interval", globalSettings.Monitoring.HealthCheckInterval,
		"metrics_enabled", globalSettings.Monitoring.MetricsEnabled)

	logger.Info("✅ 全局设置组件初始化完成")
	return nil
}

// GetName 获取服务名称
func (frs *ForwardRuleService) GetName() string {
	return frs.name
}

// GetType 获取服务类型
func (frs *ForwardRuleService) GetType() string {
	return frs.serviceType
}

// GetDependencies 获取服务依赖
func (frs *ForwardRuleService) GetDependencies() []string {
	return frs.dependencies
}

// AddRule 添加转发规则
func (frs *ForwardRuleService) AddRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("转发规则服务未运行")
	}

	// 如果有频道缓存，尝试获取频道名称并生成规则名称
	if frs.channelCache != nil && rule.Name == "" {
		sourceID := rule.GetSourceChannelID()
		targetID := rule.GetTargetChannelID()

		if sourceID != "" && targetID != "" {
			// 获取频道名称
			channelNames, err := frs.channelCache.GetChannelNames([]string{sourceID, targetID})
			if err != nil {
				logger.Warn("获取频道名称失败，使用默认命名", "error", err)
			} else {
				// 设置频道名称缓存
				rule.InputChannelName = channelNames[sourceID]
				rule.OutputChannelName = channelNames[targetID]

				// 生成基于频道名称的规则名称
				if generatedName, err := frs.channelCache.GenerateRuleName(sourceID, targetID); err == nil {
					rule.Name = generatedName
					logger.Debug("使用频道名称生成规则名称",
						"source_name", rule.InputChannelName,
						"target_name", rule.OutputChannelName,
						"rule_name", rule.Name)
				}
			}
		}
	}

	// 验证规则
	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	// 检查规则是否已存在
	if _, exists := frs.rules[rule.Name]; exists {
		return fmt.Errorf("规则 %s 已存在", rule.Name)
	}

	// 设置元数据
	now := time.Now()
	rule.Metadata.CreatedAt = now
	rule.Metadata.UpdatedAt = now
	rule.Metadata.Version = 1

	// 添加规则
	frs.rules[rule.Name] = rule
	frs.stats[rule.Name] = &types.ForwardRuleStats{}

	// 重建索引
	frs.buildIndexes()

	// 保存配置到文件（关键修复：添加配置文件保存）
	if err := frs.saveConfig(); err != nil {
		logger.Error("保存配置文件失败", "error", err, "rule", rule.Name)
		// 不返回错误，因为规则已经添加到内存中
	}

	logger.Info("转发规则已添加",
		"name", rule.Name,
		"source", rule.SourceChannelID,
		"target", rule.TargetChannelID,
		"source_name", rule.InputChannelName,
		"target_name", rule.OutputChannelName)
	return nil
}

// RemoveRule 移除转发规则
func (frs *ForwardRuleService) RemoveRule(ruleName string) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("转发规则服务未运行")
	}

	if _, exists := frs.rules[ruleName]; !exists {
		return fmt.Errorf("规则 %s 不存在", ruleName)
	}

	delete(frs.rules, ruleName)
	delete(frs.stats, ruleName)

	// 重建索引
	frs.buildIndexes()

	// 保存配置到文件（关键修复：添加配置文件保存）
	if err := frs.saveConfig(); err != nil {
		logger.Error("保存配置文件失败", "error", err, "rule", ruleName)
		// 不返回错误，因为规则已经从内存中删除
	}

	logger.Info("转发规则已移除", "name", ruleName)
	return nil
}

// UpdateRule 更新转发规则
func (frs *ForwardRuleService) UpdateRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("转发规则服务未运行")
	}

	// 验证规则
	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	// 检查规则是否存在
	oldRule, exists := frs.rules[rule.Name]
	if !exists {
		return fmt.Errorf("规则 %s 不存在", rule.Name)
	}

	// 更新元数据
	rule.Metadata.CreatedAt = oldRule.Metadata.CreatedAt
	rule.Metadata.UpdatedAt = time.Now()
	rule.Metadata.Version = oldRule.Metadata.Version + 1

	// 更新规则
	frs.rules[rule.Name] = rule

	// 重建索引
	frs.buildIndexes()

	// 保存配置到文件（关键修复：添加配置文件保存）
	if err := frs.saveConfig(); err != nil {
		logger.Error("保存配置文件失败", "error", err, "rule", rule.Name)
		// 不返回错误，因为规则已经更新到内存中
	}

	logger.Info("转发规则已更新", "name", rule.Name, "version", rule.Metadata.Version)
	return nil
}

// GetRule 获取转发规则
func (frs *ForwardRuleService) GetRule(ruleName string) (*types.ForwardRule, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rule, exists := frs.rules[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 不存在", ruleName)
	}

	// 返回副本
	ruleCopy := *rule
	return &ruleCopy, nil
}

// ListRules 列出所有转发规则
func (frs *ForwardRuleService) ListRules() []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := make([]*types.ForwardRule, 0, len(frs.rules))
	for _, rule := range frs.rules {
		ruleCopy := *rule
		rules = append(rules, &ruleCopy)
	}

	return rules
}

// GetRulesBySourceChannel 根据源频道获取规则
func (frs *ForwardRuleService) GetRulesBySourceChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := frs.sourceChannelIndex[channelID]
	if rules == nil {
		return []*types.ForwardRule{}
	}

	// 返回副本
	result := make([]*types.ForwardRule, len(rules))
	for i, rule := range rules {
		ruleCopy := *rule
		result[i] = &ruleCopy
	}

	return result
}

// GetRulesByTargetChannel 根据目标频道获取规则
func (frs *ForwardRuleService) GetRulesByTargetChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := frs.targetChannelIndex[channelID]
	if rules == nil {
		return []*types.ForwardRule{}
	}

	// 返回副本
	result := make([]*types.ForwardRule, len(rules))
	for i, rule := range rules {
		ruleCopy := *rule
		result[i] = &ruleCopy
	}

	return result
}

// ShouldForward 检查是否应该转发消息
func (frs *ForwardRuleService) ShouldForward(rule *types.ForwardRule, message interface{}) (bool, error) {
	if !rule.IsEnabled() {
		return false, nil
	}

	// 这里可以添加更多的转发条件检查逻辑
	// 例如：速率限制、内容过滤等

	return true, nil
}

// ForwardMessage 转发消息（完整实现，遵循设计文档）
func (frs *ForwardRuleService) ForwardMessage(rule *types.ForwardRule, message interface{}) error {
	if !frs.isRunning {
		return fmt.Errorf("转发规则服务未运行")
	}

	// 检查服务依赖
	if frs.queueService == nil {
		return fmt.Errorf("队列服务未注入")
	}

	// 应用速率限制
	if frs.rateLimiter != nil && !frs.rateLimiter.Allow() {
		logger.Warn("消息被速率限制拒绝", "rule", rule.Name)
		if frs.monitor != nil {
			frs.monitor.RecordForward(false)
		}
		return fmt.Errorf("消息转发被速率限制")
	}

	logger.Info("🚀 开始转发消息",
		"rule", rule.Name,
		"source_channel", rule.SourceChannelID,
		"target_channel", rule.TargetChannelID,
		"queue_service_available", frs.queueService != nil,
		"filter_service_available", frs.filterService != nil,
		"mapping_service_available", frs.mappingService != nil)

	// 1. 应用队列前过滤（基于源频道，遵循设计文档）
	if frs.filterService != nil {
		// 转换消息为可过滤的内容
		filterContent := frs.convertMessageToFilterContent(message)

		filterResult, err := frs.filterService.CheckMessage(rule.SourceChannelID, filterContent)
		if err != nil {
			logger.Error("队列前过滤检查失败", "error", err, "rule", rule.Name, "source_channel", rule.SourceChannelID)
			// 过滤失败时记录监控数据
			if frs.monitor != nil {
				frs.monitor.RecordForward(false)
			}
			return fmt.Errorf("队列前过滤检查失败: %w", err)
		}

		if !filterResult.Allowed {
			logger.Info("消息被队列前过滤规则拒绝",
				"rule", rule.Name,
				"source_channel", rule.SourceChannelID,
				"reason", filterResult.Reason,
				"matched_rules", filterResult.MatchedRules)
			rule.UpdateStats("filtered")
			if frs.monitor != nil {
				frs.monitor.RecordForward(false)
			}
			return nil // 不是错误，只是被过滤了
		}

		logger.Debug("消息通过队列前过滤检查",
			"rule", rule.Name,
			"source_channel", rule.SourceChannelID,
			"matched_rules", len(filterResult.MatchedRules))
	}

	// 2. 转换消息为Discord消息格式
	discordMessage, err := frs.convertToDiscordMessage(message)
	if err != nil {
		logger.Error("消息格式转换失败", "error", err, "rule", rule.Name)
		return fmt.Errorf("消息格式转换失败: %w", err)
	}

	// 3. 应用字段映射（如果配置了映射组）
	var mappedContent string
	var mappedEmbeds []*discordgo.MessageEmbed

	if rule.FieldMappingGroup != "" && frs.mappingService != nil {
		// 将Discord消息转换为map格式用于字段映射
		messageData := frs.convertDiscordMessageToMap(discordMessage)

		// 应用字段映射
		mappingResult, err := frs.mappingService.MapFields(messageData, rule.FieldMappingGroup)
		if err != nil {
			logger.Error("字段映射失败", "error", err, "rule", rule.Name, "mapping_group", rule.FieldMappingGroup)
			// 映射失败时使用原始内容
			mappedContent = discordMessage.Content
			mappedEmbeds = discordMessage.Embeds
		} else {
			// 使用映射后的内容
			mappedContent = mappingResult.Product.Title
			if mappingResult.Product.Description != nil {
				mappedContent += "\n" + *mappingResult.Product.Description
			}

			// 创建映射后的Embed
			if len(mappingResult.Product.Title) > 0 {
				embed := &discordgo.MessageEmbed{
					Title:       mappingResult.Product.Title,
					URL:         mappingResult.Product.URL,
					Description: "",
				}

				// 设置颜色（处理指针类型）
				if mappingResult.Product.Color != nil {
					embed.Color = *mappingResult.Product.Color
				}

				if mappingResult.Product.Description != nil {
					embed.Description = *mappingResult.Product.Description
				}

				// 添加字段
				if mappingResult.Product.ProductID != "" {
					embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
						Name:   "产品ID",
						Value:  mappingResult.Product.ProductID,
						Inline: true,
					})
				}

				if mappingResult.Product.Price != "" {
					embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
						Name:   "价格",
						Value:  mappingResult.Product.Price,
						Inline: true,
					})
				}

				mappedEmbeds = []*discordgo.MessageEmbed{embed}
			} else {
				mappedEmbeds = discordMessage.Embeds
			}

			logger.Debug("字段映射完成", "rule", rule.Name, "mapping_group", rule.FieldMappingGroup)
		}
	} else {
		// 没有配置映射组，使用原始内容
		mappedContent = discordMessage.Content
		mappedEmbeds = discordMessage.Embeds
	}

	// 4. 应用队列后过滤（基于目标频道，遵循设计文档）
	if frs.filterService != nil {
		// 使用映射后的内容进行过滤
		postFilterContent := frs.convertMappedContentToFilterContent(mappedContent, mappedEmbeds)

		postFilterResult, err := frs.filterService.CheckMessage(rule.TargetChannelID, postFilterContent)
		if err != nil {
			logger.Error("队列后过滤检查失败", "error", err, "rule", rule.Name, "target_channel", rule.TargetChannelID)
			if frs.monitor != nil {
				frs.monitor.RecordForward(false)
			}
			return fmt.Errorf("队列后过滤检查失败: %w", err)
		}

		if !postFilterResult.Allowed {
			logger.Info("消息被队列后过滤规则拒绝",
				"rule", rule.Name,
				"target_channel", rule.TargetChannelID,
				"reason", postFilterResult.Reason,
				"matched_rules", postFilterResult.MatchedRules)
			rule.UpdateStats("filtered")
			if frs.monitor != nil {
				frs.monitor.RecordForward(false)
			}
			return nil // 不是错误，只是被过滤了
		}

		logger.Debug("消息通过队列后过滤检查",
			"rule", rule.Name,
			"target_channel", rule.TargetChannelID,
			"matched_rules", len(postFilterResult.MatchedRules))
	}

	// 5. 创建消息转发任务
	task, err := frs.createForwardTask(rule, discordMessage, mappedContent, mappedEmbeds)
	if err != nil {
		logger.Error("创建转发任务失败", "error", err, "rule", rule.Name)
		if frs.monitor != nil {
			frs.monitor.RecordForward(false)
		}
		return fmt.Errorf("创建转发任务失败: %w", err)
	}

	// 6. 发布任务到队列
	logger.Info("📤 准备发布转发任务到队列",
		"rule", rule.Name,
		"task_data_keys", func() []string {
			keys := make([]string, 0, len(task))
			for k := range task {
				keys = append(keys, k)
			}
			return keys
		}())

	taskID, err := frs.publishForwardTask(task)
	if err != nil {
		logger.Error("❌ 发布转发任务失败", "error", err, "rule", rule.Name)
		if frs.monitor != nil {
			frs.monitor.RecordForward(false)
		}
		return fmt.Errorf("发布转发任务失败: %w", err)
	}

	logger.Info("✅ 转发任务发布成功",
		"rule", rule.Name,
		"task_id", taskID)

	// 7. 更新统计信息
	rule.UpdateStats("forwarded")

	// 8. 记录监控数据
	if frs.monitor != nil {
		frs.monitor.RecordForward(true)
	}

	logger.Info("🎯 消息转发任务已创建",
		"rule", rule.Name,
		"task_id", taskID,
		"target_channel", rule.TargetChannelID,
		"has_mapping", rule.FieldMappingGroup != "",
		"queue_name", "message_forward")

	return nil
}

// GetRuleStats 获取规则统计信息
func (frs *ForwardRuleService) GetRuleStats(ruleName string) (*types.ForwardRuleStats, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	stats, exists := frs.stats[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 的统计信息不存在", ruleName)
	}

	// 返回副本
	statsCopy := *stats
	return &statsCopy, nil
}

// UpdateRuleStats 更新规则统计信息
func (frs *ForwardRuleService) UpdateRuleStats(ruleName string, stats *types.ForwardRuleStats) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if _, exists := frs.stats[ruleName]; !exists {
		return fmt.Errorf("规则 %s 不存在", ruleName)
	}

	frs.stats[ruleName] = stats
	return nil
}

// ReloadConfig 重新加载配置
func (frs *ForwardRuleService) ReloadConfig() error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	logger.Info("重新加载转发规则配置", "config_file", frs.configFile)

	// 加载新配置
	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	// 验证配置
	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 重建索引
	frs.buildIndexes()

	logger.Info("转发规则配置重新加载完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// 私有方法

// loadConfig 加载配置文件
func (frs *ForwardRuleService) loadConfig() error {
	data, err := os.ReadFile(frs.configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 首先尝试新的配置格式
	newConfig := &types.ForwardRulesMainConfig{}
	if err := yaml.Unmarshal(data, newConfig); err == nil && newConfig.ForwardRules.Rules != nil {
		// 使用新格式
		frs.config = &types.ForwardRulesConfig{
			ForwardRules:   newConfig.ForwardRules.Rules,
			GlobalSettings: newConfig.GlobalSettings,
		}
	} else {
		// 回退到旧格式（向后兼容）
		config := &types.ForwardRulesConfig{}
		if err := yaml.Unmarshal(data, config); err != nil {
			return fmt.Errorf("解析配置文件失败: %w", err)
		}
		frs.config = config
	}

	frs.lastLoaded = time.Now()

	// 加载规则到内存
	frs.rules = make(map[string]*types.ForwardRule)
	frs.stats = make(map[string]*types.ForwardRuleStats)

	for i := range frs.config.ForwardRules {
		rule := &frs.config.ForwardRules[i]

		// 处理字段兼容性
		frs.normalizeRule(rule)

		// 验证规则
		if err := rule.Validate(); err != nil {
			logger.Error("转发规则验证失败", "rule", rule.Name, "error", err)
			continue
		}

		frs.rules[rule.Name] = rule
		frs.stats[rule.Name] = &rule.Stats
	}

	return nil
}

// normalizeRule 规范化规则字段（处理新旧字段名兼容性）
func (frs *ForwardRuleService) normalizeRule(rule *types.ForwardRule) {
	// 如果使用新字段名，同步到旧字段名（向后兼容）
	if rule.InputChannel != "" && rule.SourceChannelID == "" {
		rule.SourceChannelID = rule.InputChannel
	}
	if rule.OutputChannel != "" && rule.TargetChannelID == "" {
		rule.TargetChannelID = rule.OutputChannel
	}

	// 如果使用旧字段名，同步到新字段名
	if rule.SourceChannelID != "" && rule.InputChannel == "" {
		rule.InputChannel = rule.SourceChannelID
	}
	if rule.TargetChannelID != "" && rule.OutputChannel == "" {
		rule.OutputChannel = rule.TargetChannelID
	}

	// 设置默认值 - 修复逻辑
	// 检查是否有有效的频道配置，如果有则默认启用规则
	hasValidChannels := (rule.InputChannel != "" || rule.SourceChannelID != "") &&
		(rule.OutputChannel != "" || rule.TargetChannelID != "")

	if hasValidChannels && rule.Enabled == nil {
		// 只有在有有效频道配置且未明确设置 enabled 字段时才默认启用
		// nil 表示未设置，false 表示明确禁用，true 表示明确启用
		rule.SetEnabled(true)
		logger.Debug("规则默认启用（未明确设置enabled字段）",
			"input_channel", rule.InputChannel,
			"output_channel", rule.OutputChannel)
	}

	// 应用全局默认延迟设置（如果规则没有设置延迟）
	if rule.ForwardConfig.DelaySeconds == 0 && frs.config != nil && frs.config.GlobalSettings.DefaultDelaySeconds > 0 {
		rule.ForwardConfig.DelaySeconds = frs.config.GlobalSettings.DefaultDelaySeconds
		logger.Debug("应用全局默认延迟",
			"rule_delay", rule.ForwardConfig.DelaySeconds,
			"global_default", frs.config.GlobalSettings.DefaultDelaySeconds)
	}
}

// validateConfig 验证配置
func (frs *ForwardRuleService) validateConfig() error {
	if frs.config == nil {
		return fmt.Errorf("配置为空")
	}

	// 验证每个规则
	for _, rule := range frs.config.ForwardRules {
		if err := rule.Validate(); err != nil {
			return fmt.Errorf("规则 %s 验证失败: %w", rule.Name, err)
		}
	}

	return nil
}

// buildIndexes 构建索引
func (frs *ForwardRuleService) buildIndexes() {
	frs.sourceChannelIndex = make(map[string][]*types.ForwardRule)
	frs.targetChannelIndex = make(map[string][]*types.ForwardRule)

	for _, rule := range frs.rules {
		// 只索引启用的规则
		if rule.IsEnabled() {
			sourceID := rule.GetSourceChannelID()
			targetID := rule.GetTargetChannelID()

			frs.sourceChannelIndex[sourceID] = append(frs.sourceChannelIndex[sourceID], rule)
			frs.targetChannelIndex[targetID] = append(frs.targetChannelIndex[targetID], rule)
		}
	}
}

// countEnabledRules 统计启用的规则数量
func (frs *ForwardRuleService) countEnabledRules() int {
	count := 0
	for _, rule := range frs.rules {
		if rule.IsEnabled() {
			count++
		}
	}
	return count
}

// convertToDiscordMessage 转换消息为Discord消息格式
func (frs *ForwardRuleService) convertToDiscordMessage(message interface{}) (*discordgo.Message, error) {
	switch msg := message.(type) {
	case *discordgo.Message:
		return msg, nil
	case *discordgo.MessageCreate:
		return msg.Message, nil
	case *discordgo.MessageUpdate:
		return msg.Message, nil
	case map[string]interface{}:
		// 从map构造Discord消息
		discordMsg := &discordgo.Message{}

		if id, ok := msg["id"].(string); ok {
			discordMsg.ID = id
		}
		if content, ok := msg["content"].(string); ok {
			discordMsg.Content = content
		}
		if channelID, ok := msg["channel_id"].(string); ok {
			discordMsg.ChannelID = channelID
		}
		if guildID, ok := msg["guild_id"].(string); ok {
			discordMsg.GuildID = guildID
		}

		// 处理作者信息
		if author, ok := msg["author"].(map[string]interface{}); ok {
			discordMsg.Author = &discordgo.User{}
			if authorID, ok := author["id"].(string); ok {
				discordMsg.Author.ID = authorID
			}
			if username, ok := author["username"].(string); ok {
				discordMsg.Author.Username = username
			}
		}

		// 处理Embeds
		if embeds, ok := msg["embeds"].([]interface{}); ok {
			discordMsg.Embeds = make([]*discordgo.MessageEmbed, len(embeds))
			for i, embedData := range embeds {
				if embedMap, ok := embedData.(map[string]interface{}); ok {
					embed := &discordgo.MessageEmbed{}

					if title, ok := embedMap["title"].(string); ok {
						embed.Title = title
					}
					if description, ok := embedMap["description"].(string); ok {
						embed.Description = description
					}
					if url, ok := embedMap["url"].(string); ok {
						embed.URL = url
					}
					if color, ok := embedMap["color"].(int); ok {
						embed.Color = color
					}

					discordMsg.Embeds[i] = embed
				}
			}
		}

		return discordMsg, nil
	default:
		return nil, fmt.Errorf("不支持的消息类型: %T", message)
	}
}

// convertDiscordMessageToMap 将Discord消息转换为map格式
func (frs *ForwardRuleService) convertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"id":         message.ID,
		"channel_id": message.ChannelID,
		"content":    message.Content,
		"guild_id":   message.GuildID,
	}

	if message.Author != nil {
		messageMap["author"] = map[string]interface{}{
			"id":       message.Author.ID,
			"username": message.Author.Username,
		}
	}

	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embedMap := make(map[string]interface{})

			if embed.Title != "" {
				embedMap["title"] = embed.Title
			}
			if embed.Description != "" {
				embedMap["description"] = embed.Description
			}
			if embed.URL != "" {
				embedMap["url"] = embed.URL
			}
			if embed.Color != 0 {
				embedMap["color"] = embed.Color
			}

			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embeds[i] = embedMap
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// createForwardTask 创建消息转发任务
func (frs *ForwardRuleService) createForwardTask(rule *types.ForwardRule, originalMessage *discordgo.Message, mappedContent string, mappedEmbeds []*discordgo.MessageEmbed) (map[string]interface{}, error) {
	// 生成任务ID
	taskID := fmt.Sprintf("forward_%s_%d", rule.Name, time.Now().UnixNano())

	// 序列化Embeds为JSON
	var embedsJSON string
	if len(mappedEmbeds) > 0 {
		embedsBytes, err := json.Marshal(mappedEmbeds)
		if err != nil {
			logger.Error("序列化Embeds失败", "error", err, "rule", rule.Name)
			embedsJSON = ""
		} else {
			embedsJSON = string(embedsBytes)
		}
	}

	// 创建任务数据
	task := map[string]interface{}{
		"id":                taskID,
		"original_message":  originalMessage.Content,
		"processed_message": mappedContent,
		"target_channels":   []string{rule.TargetChannelID},
		"source_channel":    rule.SourceChannelID,
		"author_id":         originalMessage.Author.ID,
		"author_name":       originalMessage.Author.Username,
		"guild_id":          originalMessage.GuildID,
		"message_id":        originalMessage.ID,
		"created_at":        time.Now(),
		"mapping_name":      rule.FieldMappingGroup,
		"timestamp":         originalMessage.Timestamp,
		"embeds_json":       embedsJSON,
		"delay_seconds":     rule.ForwardConfig.DelaySeconds, // 添加延迟设置
	}

	// 如果有旧格式的Embeds，也添加进去（向后兼容）
	if len(mappedEmbeds) > 0 {
		embedMaps := make([]map[string]interface{}, len(mappedEmbeds))
		for i, embed := range mappedEmbeds {
			embedMap := map[string]interface{}{
				"title":       embed.Title,
				"description": embed.Description,
				"url":         embed.URL,
				"color":       embed.Color,
			}

			if len(embed.Fields) > 0 {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embedMaps[i] = embedMap
		}
		task["embeds"] = embedMaps
	}

	return task, nil
}

// publishForwardTask 发布转发任务到队列
func (frs *ForwardRuleService) publishForwardTask(task map[string]interface{}) (string, error) {
	if frs.queueService == nil {
		return "", fmt.Errorf("队列服务未注入")
	}

	// 发布任务到消息转发队列
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用延迟发布（如果配置了延迟）
	publishOptions := types.PublishOptions{
		Priority: 1,
		Headers:  make(map[string]interface{}),
	}

	// 确定延迟时间（规则级别优先，然后全局默认值）
	var delaySeconds int
	if taskData, ok := task["delay_seconds"]; ok {
		if delay, ok := taskData.(int); ok && delay > 0 {
			delaySeconds = delay
		}
	}

	// 如果任务中没有延迟设置，使用全局默认值
	if delaySeconds == 0 && frs.config != nil {
		delaySeconds = frs.config.GlobalSettings.DefaultDelaySeconds
	}

	// 根据延迟设置选择发布方式
	if delaySeconds > 0 {
		// 使用延迟发布
		delay := time.Duration(delaySeconds) * time.Second
		logger.Info("📤 发布延迟转发任务到队列",
			"queue", "message_forward",
			"delay_seconds", delaySeconds,
			"delay_duration", delay)

		taskID, err := frs.queueService.PublishDelayedTask(
			ctx,
			"message_forward",
			"message_forward",
			task,
			delay,
			publishOptions,
		)
		if err != nil {
			logger.Error("❌ 发布延迟任务失败", "error", err)
			return "", fmt.Errorf("发布延迟任务失败: %w", err)
		}
		logger.Info("✅ 延迟任务发布成功", "task_id", taskID, "delay", delay)
		return taskID, nil
	} else {
		// 立即发布
		logger.Info("📤 发布立即转发任务到队列", "queue", "message_forward", "task_type", "message_forward")
		taskID, err := frs.queueService.PublishTask(
			ctx,
			"message_forward",
			"message_forward",
			task,
			publishOptions,
		)
		if err != nil {
			logger.Error("❌ 发布任务失败", "error", err)
			return "", fmt.Errorf("发布任务失败: %w", err)
		}
		logger.Info("✅ 立即任务发布成功", "task_id", taskID)
		return taskID, nil
	}
}

// RateLimiter 速率限制器（实现全局设置中的速率限制）
type RateLimiter struct {
	maxMessages int
	timeWindow  time.Duration
	messages    []time.Time
	mu          sync.Mutex
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(maxMessages int, timeWindow time.Duration) *RateLimiter {
	return &RateLimiter{
		maxMessages: maxMessages,
		timeWindow:  timeWindow,
		messages:    make([]time.Time, 0),
	}
}

// Allow 检查是否允许发送消息
func (rl *RateLimiter) Allow() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()

	// 清理过期的消息记录
	cutoff := now.Add(-rl.timeWindow)
	validMessages := make([]time.Time, 0)
	for _, msgTime := range rl.messages {
		if msgTime.After(cutoff) {
			validMessages = append(validMessages, msgTime)
		}
	}
	rl.messages = validMessages

	// 检查是否超过限制
	if len(rl.messages) >= rl.maxMessages {
		return false
	}

	// 记录新消息
	rl.messages = append(rl.messages, now)
	return true
}

// ErrorHandler 错误处理器（实现全局设置中的错误处理）
type ErrorHandler struct {
	maxRetries     int
	retryDelay     time.Duration
	logErrors      bool
	notifyOnError  bool
	errorChannelID string
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(config types.ErrorHandlingConfig) *ErrorHandler {
	return &ErrorHandler{
		maxRetries:     config.MaxRetries,
		retryDelay:     config.RetryDelay,
		logErrors:      config.LogErrors,
		notifyOnError:  config.NotifyOnError,
		errorChannelID: config.ErrorChannelID,
	}
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(err error, context string, retryCount int) bool {
	if eh.logErrors {
		logger.Error("转发错误", "error", err, "context", context, "retry_count", retryCount)
	}

	// 检查是否应该重试
	if retryCount < eh.maxRetries {
		logger.Info("准备重试", "context", context, "retry_count", retryCount+1, "delay", eh.retryDelay)
		time.Sleep(eh.retryDelay)
		return true // 应该重试
	}

	// 达到最大重试次数
	if eh.notifyOnError && eh.errorChannelID != "" {
		// TODO: 发送错误通知到指定频道
		logger.Warn("错误通知功能尚未实现", "error_channel", eh.errorChannelID)
	}

	return false // 不再重试
}

// ForwardMonitor 转发监控器（实现全局设置中的监控功能）
type ForwardMonitor struct {
	enabled             bool
	statsInterval       time.Duration
	healthCheckInterval time.Duration
	metricsEnabled      bool

	// 统计数据
	totalForwards      int64
	successfulForwards int64
	failedForwards     int64
	lastStatsUpdate    time.Time

	// 监控状态
	isRunning bool
	stopChan  chan struct{}
	mu        sync.RWMutex
}

// NewForwardMonitor 创建转发监控器
func NewForwardMonitor(config types.MonitoringConfig) *ForwardMonitor {
	return &ForwardMonitor{
		enabled:             config.Enabled,
		statsInterval:       config.StatsInterval,
		healthCheckInterval: config.HealthCheckInterval,
		metricsEnabled:      config.MetricsEnabled,
		stopChan:            make(chan struct{}),
	}
}

// Start 启动监控
func (fm *ForwardMonitor) Start() {
	if !fm.enabled {
		return
	}

	fm.mu.Lock()
	if fm.isRunning {
		fm.mu.Unlock()
		return
	}
	fm.isRunning = true
	fm.mu.Unlock()

	go fm.runStatsCollection()
	go fm.runHealthCheck()

	logger.Info("转发监控器已启动",
		"stats_interval", fm.statsInterval,
		"health_check_interval", fm.healthCheckInterval,
		"metrics_enabled", fm.metricsEnabled)
}

// Stop 停止监控
func (fm *ForwardMonitor) Stop() {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	if !fm.isRunning {
		return
	}

	close(fm.stopChan)
	fm.isRunning = false
	logger.Info("转发监控器已停止")
}

// RecordForward 记录转发操作
func (fm *ForwardMonitor) RecordForward(success bool) {
	if !fm.enabled {
		return
	}

	fm.mu.Lock()
	defer fm.mu.Unlock()

	fm.totalForwards++
	if success {
		fm.successfulForwards++
	} else {
		fm.failedForwards++
	}
}

// runStatsCollection 运行统计收集
func (fm *ForwardMonitor) runStatsCollection() {
	ticker := time.NewTicker(fm.statsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			fm.collectStats()
		case <-fm.stopChan:
			return
		}
	}
}

// runHealthCheck 运行健康检查
func (fm *ForwardMonitor) runHealthCheck() {
	ticker := time.NewTicker(fm.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			fm.performHealthCheck()
		case <-fm.stopChan:
			return
		}
	}
}

// collectStats 收集统计信息
func (fm *ForwardMonitor) collectStats() {
	fm.mu.RLock()
	total := fm.totalForwards
	successful := fm.successfulForwards
	failed := fm.failedForwards
	fm.mu.RUnlock()

	successRate := float64(0)
	if total > 0 {
		successRate = float64(successful) / float64(total) * 100
	}

	logger.Info("转发统计",
		"total_forwards", total,
		"successful", successful,
		"failed", failed,
		"success_rate", fmt.Sprintf("%.2f%%", successRate))

	fm.mu.Lock()
	fm.lastStatsUpdate = time.Now()
	fm.mu.Unlock()
}

// performHealthCheck 执行健康检查
func (fm *ForwardMonitor) performHealthCheck() {
	fm.mu.RLock()
	total := fm.totalForwards
	failed := fm.failedForwards
	lastUpdate := fm.lastStatsUpdate
	fm.mu.RUnlock()

	// 检查错误率
	errorRate := float64(0)
	if total > 0 {
		errorRate = float64(failed) / float64(total) * 100
	}

	// 检查是否长时间没有更新
	timeSinceLastUpdate := time.Since(lastUpdate)

	if errorRate > 10.0 {
		logger.Warn("转发错误率过高", "error_rate", fmt.Sprintf("%.2f%%", errorRate))
	}

	if timeSinceLastUpdate > fm.statsInterval*2 {
		logger.Warn("统计更新延迟", "delay", timeSinceLastUpdate)
	}

	logger.Debug("健康检查完成", "error_rate", fmt.Sprintf("%.2f%%", errorRate))
}

// ChannelNameCacheInterface 频道名称缓存接口
type ChannelNameCacheInterface interface {
	GetChannelName(channelID string) (string, error)
	GetChannelNames(channelIDs []string) (map[string]string, error)
	GenerateRuleName(sourceChannelID, targetChannelID string) (string, error)
}

// convertMessageToFilterContent 转换消息为可过滤的内容
func (frs *ForwardRuleService) convertMessageToFilterContent(message interface{}) string {
	switch msg := message.(type) {
	case *discordgo.Message:
		return frs.extractFilterableContent(msg)
	case *discordgo.MessageCreate:
		return frs.extractFilterableContent(msg.Message)
	case *discordgo.MessageUpdate:
		return frs.extractFilterableContent(msg.Message)
	case string:
		return msg
	case map[string]interface{}:
		// 从map中提取内容
		if content, ok := msg["content"].(string); ok {
			return content
		}
		return fmt.Sprintf("%v", msg)
	default:
		return fmt.Sprintf("%v", message)
	}
}

// convertMappedContentToFilterContent 转换映射后的内容为可过滤的内容
func (frs *ForwardRuleService) convertMappedContentToFilterContent(content string, embeds []*discordgo.MessageEmbed) string {
	filterContent := content

	// 添加Embed内容到过滤内容中
	for _, embed := range embeds {
		if embed.Title != "" {
			filterContent += " " + embed.Title
		}
		if embed.Description != "" {
			filterContent += " " + embed.Description
		}
		if embed.URL != "" {
			filterContent += " " + embed.URL
		}

		// 添加字段内容
		for _, field := range embed.Fields {
			filterContent += " " + field.Name + " " + field.Value
		}
	}

	return filterContent
}

// extractFilterableContent 从Discord消息中提取可过滤的内容
func (frs *ForwardRuleService) extractFilterableContent(message *discordgo.Message) string {
	content := message.Content

	// 添加Embed内容
	for _, embed := range message.Embeds {
		if embed.Title != "" {
			content += " " + embed.Title
		}
		if embed.Description != "" {
			content += " " + embed.Description
		}
		if embed.URL != "" {
			content += " " + embed.URL
		}

		// 添加字段内容
		for _, field := range embed.Fields {
			content += " " + field.Name + " " + field.Value
		}
	}

	// 使用ProductExtractor提取产品信息并添加到过滤内容
	extractor := types.NewProductExtractor()
	messageData := frs.convertDiscordMessageToMap(message)
	product := extractor.ExtractFromDiscordMessage(messageData, message.Content, message.ChannelID)

	// 添加产品信息到过滤内容
	if product.ProductID != "" {
		content += " " + product.ProductID
	}
	if product.Title != "" {
		content += " " + product.Title
	}
	if product.URL != "" {
		content += " " + product.URL
	}
	if product.Description != nil && *product.Description != "" {
		content += " " + *product.Description
	}

	return content
}

// saveConfig 保存配置到文件（优化：只保存必要字段，保持简洁）
func (frs *ForwardRuleService) saveConfig() error {
	// 创建简化的规则列表
	simplifiedRules := make([]map[string]interface{}, 0, len(frs.rules))

	// 转换规则为简化格式（只保存非默认值）
	for _, rule := range frs.rules {
		simplifiedRule := map[string]interface{}{
			"input_channel":       rule.InputChannel,
			"output_channel":      rule.OutputChannel,
			"field_mapping_group": rule.FieldMappingGroup,
		}

		// 只在非默认值时添加可选字段
		if rule.Name != "" && !frs.isAutoGeneratedName(rule.Name, rule.InputChannel, rule.OutputChannel) {
			simplifiedRule["name"] = rule.Name
		}

		// 只有明确禁用时才保存 enabled: false
		if rule.Enabled != nil && !*rule.Enabled {
			simplifiedRule["enabled"] = false
		}
		// 注意：enabled: true 是默认值，不需要保存

		// 只有非零延迟时才保存 delay_seconds
		if rule.ForwardConfig.DelaySeconds > 0 {
			simplifiedRule["delay_seconds"] = rule.ForwardConfig.DelaySeconds
		}
		// 注意：delay_seconds: 0 是默认值（立即转发），不需要保存

		// 只有非默认模式时才保存 mode
		if rule.ForwardConfig.Mode != "" && rule.ForwardConfig.Mode != "transform" {
			simplifiedRule["mode"] = rule.ForwardConfig.Mode
		}

		simplifiedRules = append(simplifiedRules, simplifiedRule)
	}

	// 创建配置结构
	var configData []byte
	var err error

	// 检查当前配置格式
	if frs.config != nil && frs.config.GlobalSettings != (types.GlobalForwardSettings{}) {
		// 使用新格式（包含全局设置）
		mainConfig := map[string]interface{}{
			"forward_rules": map[string]interface{}{
				"enabled":                   true,
				"field_mapping_groups_file": "configs/field_mapping_groups.yaml",
				"rules":                     simplifiedRules,
			},
			"global_settings": frs.config.GlobalSettings,
		}

		// 序列化为YAML
		configData, err = yaml.Marshal(mainConfig)
	} else {
		// 使用旧格式（向后兼容）
		config := map[string]interface{}{
			"forward_rules": simplifiedRules,
		}

		// 序列化为YAML
		configData, err = yaml.Marshal(config)
	}

	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(frs.configFile, configData, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	logger.Info("转发规则配置已保存",
		"file", frs.configFile,
		"rules_count", len(frs.rules))

	return nil
}

// isAutoGeneratedName 检查规则名称是否是自动生成的
func (frs *ForwardRuleService) isAutoGeneratedName(name, inputChannel, outputChannel string) bool {
	// 自动生成的名称格式：forward_{input_suffix}_{output_suffix}
	// 例如：forward_634064_to_307304

	if len(inputChannel) < 6 || len(outputChannel) < 6 {
		return false
	}

	// 获取频道ID的后6位
	inputSuffix := inputChannel[len(inputChannel)-6:]
	outputSuffix := outputChannel[len(outputChannel)-6:]

	// 构造预期的自动生成名称
	expectedName := fmt.Sprintf("forward_%s_to_%s", inputSuffix, outputSuffix)

	return name == expectedName
}
