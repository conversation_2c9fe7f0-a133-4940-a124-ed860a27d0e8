package mapping

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"gopkg.in/yaml.v3"
)

// FieldMappingService 字段映射服务
// 实现 types.Service 接口，管理字段映射组的加载、缓存和应用
type FieldMappingService struct {
	// 服务基础信息
	name         string
	serviceType  string
	dependencies []string

	// 配置和状态
	configFile   string
	config       *types.FieldMappingGroupsConfig
	mappingCache map[string]*types.FieldMappingGroup
	mu           sync.RWMutex

	// 运行状态
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc

	// 缓存管理
	cacheEnabled bool
	cacheTTL     time.Duration
	cacheMaxSize int
	lastLoaded   time.Time
}

// NewFieldMappingService 创建字段映射服务
func NewFieldMappingService(configFile string) *FieldMappingService {
	ctx, cancel := context.WithCancel(context.Background())

	return &FieldMappingService{
		name:         "FieldMappingService",
		serviceType:  "mapping",
		dependencies: []string{}, // 无依赖
		configFile:   configFile,
		mappingCache: make(map[string]*types.FieldMappingGroup),
		ctx:          ctx,
		cancel:       cancel,
		cacheEnabled: true,
		cacheTTL:     time.Hour,
		cacheMaxSize: 100,
	}
}

// Initialize 初始化服务
func (fms *FieldMappingService) Initialize(ctx context.Context) error {
	logger.Info("初始化字段映射服务", "config_file", fms.configFile)

	// 加载配置文件
	if err := fms.loadConfig(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 验证配置
	if err := fms.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 应用全局设置
	fms.applyGlobalSettings()

	logger.Info("字段映射服务初始化完成",
		"mapping_groups", len(fms.config.FieldMappingGroups),
		"cache_enabled", fms.cacheEnabled)

	return nil
}

// Start 启动服务
func (fms *FieldMappingService) Start(ctx context.Context) error {
	fms.mu.Lock()
	defer fms.mu.Unlock()

	if fms.isRunning {
		return fmt.Errorf("字段映射服务已在运行")
	}

	fms.isRunning = true
	logger.Info("字段映射服务已启动")

	return nil
}

// Stop 停止服务
func (fms *FieldMappingService) Stop(ctx context.Context) error {
	fms.mu.Lock()
	defer fms.mu.Unlock()

	if !fms.isRunning {
		return nil
	}

	fms.cancel()
	fms.isRunning = false

	// 清理缓存
	fms.mappingCache = make(map[string]*types.FieldMappingGroup)

	logger.Info("字段映射服务已停止")
	return nil
}

// HealthCheck 健康检查
func (fms *FieldMappingService) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	fms.mu.RLock()
	defer fms.mu.RUnlock()

	startTime := time.Now()

	// 检查服务状态
	if !fms.isRunning {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "服务未运行",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(fms.configFile); os.IsNotExist(err) {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      fmt.Sprintf("配置文件不存在: %s", fms.configFile),
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	// 检查映射组数量
	if len(fms.config.FieldMappingGroups) == 0 {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "没有可用的字段映射组",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	return &services.HealthCheckResult{
		Healthy:      true,
		Message:      fmt.Sprintf("服务正常，已加载 %d 个映射组", len(fms.config.FieldMappingGroups)),
		CheckTime:    startTime,
		ResponseTime: time.Since(startTime),
	}
}

// GetName 获取服务名称
func (fms *FieldMappingService) GetName() string {
	return fms.name
}

// GetType 获取服务类型
func (fms *FieldMappingService) GetType() string {
	return fms.serviceType
}

// GetDependencies 获取服务依赖
func (fms *FieldMappingService) GetDependencies() []string {
	return fms.dependencies
}

// MapFields 应用字段映射
func (fms *FieldMappingService) MapFields(sourceData map[string]interface{}, mappingGroupName string) (*types.MappingResult, error) {
	fms.mu.RLock()
	defer fms.mu.RUnlock()

	if !fms.isRunning {
		return nil, fmt.Errorf("字段映射服务未运行")
	}

	// 获取映射组
	mappingGroup, err := fms.getMappingGroup(mappingGroupName)
	if err != nil {
		return nil, fmt.Errorf("获取映射组失败: %w", err)
	}

	// 应用映射
	result, err := mappingGroup.ApplyMapping(sourceData)
	if err != nil {
		return nil, fmt.Errorf("应用映射失败: %w", err)
	}

	logger.Debug("字段映射完成",
		"mapping_group", mappingGroupName,
		"applied_rules", len(result.AppliedRules),
		"errors", len(result.Errors),
		"warnings", len(result.Warnings))

	return result, nil
}

// GetMappingGroup 获取映射组
func (fms *FieldMappingService) GetMappingGroup(name string) (*types.FieldMappingGroup, error) {
	fms.mu.RLock()
	defer fms.mu.RUnlock()

	return fms.getMappingGroup(name)
}

// ListMappingGroups 列出所有映射组
func (fms *FieldMappingService) ListMappingGroups() []string {
	fms.mu.RLock()
	defer fms.mu.RUnlock()

	groups := make([]string, 0, len(fms.config.FieldMappingGroups))
	for name := range fms.config.FieldMappingGroups {
		groups = append(groups, name)
	}

	return groups
}

// ReloadConfig 重新加载配置
func (fms *FieldMappingService) ReloadConfig() error {
	fms.mu.Lock()
	defer fms.mu.Unlock()

	logger.Info("重新加载字段映射配置", "config_file", fms.configFile)

	// 加载新配置
	if err := fms.loadConfig(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	// 清理缓存
	fms.mappingCache = make(map[string]*types.FieldMappingGroup)

	// 应用全局设置
	fms.applyGlobalSettings()

	logger.Info("字段映射配置重新加载完成",
		"mapping_groups", len(fms.config.FieldMappingGroups))

	return nil
}

// 私有方法

// loadConfig 加载配置文件
func (fms *FieldMappingService) loadConfig() error {
	data, err := os.ReadFile(fms.configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	config := &types.FieldMappingGroupsConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	fms.config = config
	fms.lastLoaded = time.Now()

	return nil
}

// validateConfig 验证配置
func (fms *FieldMappingService) validateConfig() error {
	if fms.config == nil {
		return fmt.Errorf("配置为空")
	}

	if len(fms.config.FieldMappingGroups) == 0 {
		return fmt.Errorf("没有定义字段映射组")
	}

	// 验证每个映射组
	for name, group := range fms.config.FieldMappingGroups {
		if err := group.Validate(); err != nil {
			return fmt.Errorf("映射组 %s 验证失败: %w", name, err)
		}
	}

	// 检查默认映射组是否存在
	defaultGroup := fms.config.GlobalSettings.DefaultMappingGroup
	if defaultGroup != "" {
		if _, exists := fms.config.FieldMappingGroups[defaultGroup]; !exists {
			return fmt.Errorf("默认映射组 %s 不存在", defaultGroup)
		}
	}

	return nil
}

// applyGlobalSettings 应用全局设置
func (fms *FieldMappingService) applyGlobalSettings() {
	settings := fms.config.GlobalSettings

	fms.cacheEnabled = settings.CacheSettings.Enabled
	if settings.CacheSettings.TTL > 0 {
		fms.cacheTTL = settings.CacheSettings.TTL
	}
	if settings.CacheSettings.MaxSize > 0 {
		fms.cacheMaxSize = settings.CacheSettings.MaxSize
	}
}

// getMappingGroup 获取映射组（内部方法，不加锁）
func (fms *FieldMappingService) getMappingGroup(name string) (*types.FieldMappingGroup, error) {
	// 如果名称为空，使用默认映射组
	if name == "" {
		name = fms.config.GlobalSettings.DefaultMappingGroup
		if name == "" {
			name = "default"
		}
	}

	// 尝试从缓存获取
	if fms.cacheEnabled {
		if cached, exists := fms.mappingCache[name]; exists {
			return cached, nil
		}
	}

	// 从配置中获取
	group, exists := fms.config.FieldMappingGroups[name]
	if !exists {
		return nil, fmt.Errorf("映射组 %s 不存在", name)
	}

	// 复制到缓存
	if fms.cacheEnabled && len(fms.mappingCache) < fms.cacheMaxSize {
		groupCopy := group // 结构体复制
		fms.mappingCache[name] = &groupCopy
	}

	return &group, nil
}
