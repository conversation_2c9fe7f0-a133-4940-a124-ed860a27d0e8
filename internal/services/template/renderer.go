package template

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// Renderer 模板渲染器
type Renderer struct {
	// 可以添加配置选项
}

// NewRenderer 创建新的模板渲染器
func NewRenderer() *Renderer {
	return &Renderer{}
}

// RenderContext 渲染上下文
type RenderContext struct {
	Template  *types.Template
	Variables map[string]interface{}
	GuildID   string
}

// RenderedContent 渲染结果
type RenderedContent struct {
	Content      string                     `json:"content,omitempty"`
	Embeds       []*discordgo.MessageEmbed  `json:"embeds,omitempty"`
	Components   []discordgo.MessageComponent `json:"components,omitempty"`
	Files        []*discordgo.File          `json:"files,omitempty"`
	RenderingTime time.Duration             `json:"rendering_time"`
}

// Render 渲染模板
func (r *Renderer) Render(ctx RenderContext) (*RenderedContent, error) {
	startTime := time.Now()

	// 验证模板变量
	if err := r.validateVariables(ctx.Template, ctx.Variables); err != nil {
		return nil, fmt.Errorf("变量验证失败: %w", err)
	}

	result := &RenderedContent{}

	// 处理模板内容
	if err := r.renderContent(ctx, result); err != nil {
		return nil, fmt.Errorf("渲染内容失败: %w", err)
	}

	result.RenderingTime = time.Since(startTime)

	logger.Debug("🎨 模板渲染完成",
		"template_id", ctx.Template.ID,
		"rendering_time", result.RenderingTime,
		"has_content", result.Content != "",
		"embeds_count", len(result.Embeds))

	return result, nil
}

// renderContent 渲染模板内容
func (r *Renderer) renderContent(ctx RenderContext, result *RenderedContent) error {
	content := ctx.Template.Content

	// 处理不同类型的内容
	switch v := content.(type) {
	case string:
		// 简单文本内容
		result.Content = r.replaceVariables(v, ctx.Variables)
	case map[string]interface{}:
		// 复杂内容对象
		if err := r.renderComplexContent(v, ctx.Variables, result); err != nil {
			return err
		}
	default:
		// 尝试将内容转换为 JSON 再处理
		jsonData, err := json.Marshal(content)
		if err != nil {
			return fmt.Errorf("无法处理的内容类型: %T", content)
		}

		var contentMap map[string]interface{}
		if err := json.Unmarshal(jsonData, &contentMap); err != nil {
			return fmt.Errorf("内容格式错误: %w", err)
		}

		if err := r.renderComplexContent(contentMap, ctx.Variables, result); err != nil {
			return err
		}
	}

	return nil
}

// renderComplexContent 渲染复杂内容
func (r *Renderer) renderComplexContent(content map[string]interface{}, variables map[string]interface{}, result *RenderedContent) error {
	// 处理文本内容
	if textContent, exists := content["content"]; exists {
		if text, ok := textContent.(string); ok {
			result.Content = r.replaceVariables(text, variables)
		}
	}

	// 处理嵌入消息
	if embedData, exists := content["embed"]; exists {
		embed, err := r.renderEmbed(embedData, variables)
		if err != nil {
			return fmt.Errorf("渲染嵌入消息失败: %w", err)
		}
		result.Embeds = append(result.Embeds, embed)
	}

	// 处理多个嵌入消息
	if embedsData, exists := content["embeds"]; exists {
		if embedsList, ok := embedsData.([]interface{}); ok {
			for _, embedData := range embedsList {
				embed, err := r.renderEmbed(embedData, variables)
				if err != nil {
					logger.Warn("渲染嵌入消息失败", "error", err)
					continue
				}
				result.Embeds = append(result.Embeds, embed)
			}
		}
	}

	return nil
}

// renderEmbed 渲染嵌入消息
func (r *Renderer) renderEmbed(embedData interface{}, variables map[string]interface{}) (*discordgo.MessageEmbed, error) {
	// 将 embedData 转换为 map
	var embedMap map[string]interface{}
	
	switch v := embedData.(type) {
	case map[string]interface{}:
		embedMap = v
	default:
		jsonData, err := json.Marshal(embedData)
		if err != nil {
			return nil, fmt.Errorf("无法序列化嵌入数据: %w", err)
		}
		if err := json.Unmarshal(jsonData, &embedMap); err != nil {
			return nil, fmt.Errorf("无法解析嵌入数据: %w", err)
		}
	}

	embed := &discordgo.MessageEmbed{}

	// 渲染标题
	if title, exists := embedMap["title"]; exists {
		if titleStr, ok := title.(string); ok {
			embed.Title = r.replaceVariables(titleStr, variables)
		}
	}

	// 渲染描述
	if description, exists := embedMap["description"]; exists {
		if descStr, ok := description.(string); ok {
			embed.Description = r.replaceVariables(descStr, variables)
		}
	}

	// 渲染颜色
	if color, exists := embedMap["color"]; exists {
		embed.Color = r.parseColor(color)
	}

	// 渲染字段
	if fields, exists := embedMap["fields"]; exists {
		if fieldsList, ok := fields.([]interface{}); ok {
			for _, fieldData := range fieldsList {
				if fieldMap, ok := fieldData.(map[string]interface{}); ok {
					field := &discordgo.MessageEmbedField{}
					
					if name, exists := fieldMap["name"]; exists {
						if nameStr, ok := name.(string); ok {
							field.Name = r.replaceVariables(nameStr, variables)
						}
					}
					
					if value, exists := fieldMap["value"]; exists {
						if valueStr, ok := value.(string); ok {
							field.Value = r.replaceVariables(valueStr, variables)
						}
					}
					
					if inline, exists := fieldMap["inline"]; exists {
						if inlineBool, ok := inline.(bool); ok {
							field.Inline = inlineBool
						}
					}
					
					embed.Fields = append(embed.Fields, field)
				}
			}
		}
	}

	// 渲染页脚
	if footer, exists := embedMap["footer"]; exists {
		if footerMap, ok := footer.(map[string]interface{}); ok {
			embed.Footer = &discordgo.MessageEmbedFooter{}
			if text, exists := footerMap["text"]; exists {
				if textStr, ok := text.(string); ok {
					embed.Footer.Text = r.replaceVariables(textStr, variables)
				}
			}
			if iconURL, exists := footerMap["icon_url"]; exists {
				if iconStr, ok := iconURL.(string); ok {
					embed.Footer.IconURL = r.replaceVariables(iconStr, variables)
				}
			}
		} else if footerStr, ok := footer.(string); ok {
			embed.Footer = &discordgo.MessageEmbedFooter{
				Text: r.replaceVariables(footerStr, variables),
			}
		}
	}

	// 渲染缩略图
	if thumbnail, exists := embedMap["thumbnail"]; exists {
		if thumbnailStr, ok := thumbnail.(string); ok {
			embed.Thumbnail = &discordgo.MessageEmbedThumbnail{
				URL: r.replaceVariables(thumbnailStr, variables),
			}
		}
	}

	// 渲染图片
	if image, exists := embedMap["image"]; exists {
		if imageStr, ok := image.(string); ok {
			embed.Image = &discordgo.MessageEmbedImage{
				URL: r.replaceVariables(imageStr, variables),
			}
		}
	}

	// 渲染时间戳
	if timestamp, exists := embedMap["timestamp"]; exists {
		if timestampBool, ok := timestamp.(bool); ok && timestampBool {
			embed.Timestamp = time.Now().Format(time.RFC3339)
		} else if timestampStr, ok := timestamp.(string); ok {
			embed.Timestamp = r.replaceVariables(timestampStr, variables)
		}
	}

	return embed, nil
}

// replaceVariables 替换变量占位符
func (r *Renderer) replaceVariables(text string, variables map[string]interface{}) string {
	if text == "" {
		return text
	}

	// 处理简单的 {{variable}} 格式
	simplePattern := regexp.MustCompile(`\{\{([^}]+)\}\}`)
	text = simplePattern.ReplaceAllStringFunc(text, func(match string) string {
		// 提取变量名
		varName := strings.TrimSpace(match[2 : len(match)-2])
		
		// 获取变量值
		value := r.getNestedValue(variables, varName)
		if value == nil {
			logger.Warn("模板变量未找到", "variable", varName)
			return match // 保留原始占位符
		}
		
		return r.formatValue(value)
	})

	// 处理 Handlebars 风格的条件语句 {{#if condition}}...{{/if}}
	text = r.processConditionals(text, variables)

	return text
}

// processConditionals 处理条件语句
func (r *Renderer) processConditionals(text string, variables map[string]interface{}) string {
	// 简化的条件处理，支持 {{#if variable}}...{{else}}...{{/if}}
	ifPattern := regexp.MustCompile(`\{\{#if\s+([^}]+)\}\}(.*?)\{\{/if\}\}`)
	
	return ifPattern.ReplaceAllStringFunc(text, func(match string) string {
		// 这里可以实现更复杂的条件逻辑
		// 暂时返回原文本
		return match
	})
}

// getNestedValue 获取嵌套变量值
func (r *Renderer) getNestedValue(variables map[string]interface{}, path string) interface{} {
	parts := strings.Split(path, ".")
	current := variables
	
	for i, part := range parts {
		if i == len(parts)-1 {
			// 最后一个部分
			return current[part]
		}
		
		// 中间部分，需要继续嵌套
		if next, exists := current[part]; exists {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				return nil
			}
		} else {
			return nil
		}
	}
	
	return nil
}

// formatValue 格式化值
func (r *Renderer) formatValue(value interface{}) string {
	if value == nil {
		return ""
	}
	
	switch v := value.(type) {
	case string:
		return v
	case int, int32, int64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	case time.Time:
		return v.Format("2006-01-02 15:04:05")
	default:
		return fmt.Sprintf("%v", v)
	}
}

// parseColor 解析颜色值
func (r *Renderer) parseColor(color interface{}) int {
	switch v := color.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		// 处理十六进制颜色
		if strings.HasPrefix(v, "#") {
			if colorInt, err := strconv.ParseInt(v[1:], 16, 32); err == nil {
				return int(colorInt)
			}
		}
		// 处理预定义颜色名称
		return r.getNamedColor(v)
	default:
		return 0
	}
}

// getNamedColor 获取预定义颜色
func (r *Renderer) getNamedColor(name string) int {
	colors := map[string]int{
		"red":    0xff0000,
		"green":  0x00ff00,
		"blue":   0x0000ff,
		"yellow": 0xffff00,
		"orange": 0xff9900,
		"purple": 0x9900ff,
		"pink":   0xff00ff,
		"white":  0xffffff,
		"black":  0x000000,
	}
	
	if color, exists := colors[strings.ToLower(name)]; exists {
		return color
	}
	
	return 0
}

// validateVariables 验证模板变量
func (r *Renderer) validateVariables(template *types.Template, variables map[string]interface{}) error {
	for varName, varDef := range template.Variables {
		value, exists := variables[varName]
		
		// 检查必需变量
		if varDef.Required && !exists {
			return fmt.Errorf("缺少必需变量: %s", varName)
		}
		
		// 如果变量不存在但有默认值，使用默认值
		if !exists && varDef.DefaultValue != nil {
			variables[varName] = varDef.DefaultValue
			continue
		}
		
		// 验证变量类型
		if exists && !r.validateVariableType(value, varDef.Type) {
			return fmt.Errorf("变量 %s 类型错误，期望 %s", varName, varDef.Type)
		}
	}
	
	return nil
}

// validateVariableType 验证变量类型
func (r *Renderer) validateVariableType(value interface{}, expectedType string) bool {
	if value == nil {
		return true // nil 值总是有效的
	}
	
	actualType := reflect.TypeOf(value).Kind()
	
	switch expectedType {
	case "string":
		return actualType == reflect.String
	case "int", "integer":
		return actualType == reflect.Int || actualType == reflect.Int32 || actualType == reflect.Int64
	case "float", "number":
		return actualType == reflect.Float32 || actualType == reflect.Float64
	case "bool", "boolean":
		return actualType == reflect.Bool
	case "object":
		return actualType == reflect.Map || actualType == reflect.Struct
	case "array":
		return actualType == reflect.Slice || actualType == reflect.Array
	default:
		return true // 未知类型，允许通过
	}
}
