{"templateId": "system_notification", "name": "系统通知", "description": "用于发送系统状态、错误警告等重要通知", "type": "EMBED", "category": "system", "version": "1.0.0", "isActive": true, "guildId": null, "accessLevel": "ADMIN", "content": {"embed": {"title": "{{#if level}}{{getStatusIcon level}}{{/if}} {{title}}", "description": "{{message}}", "color": "{{getStatusColor level}}", "fields": [{"name": "⏰ 时间", "value": "{{formatDate timestamp 'YYYY-MM-DD HH:mm:ss'}}", "inline": true}], "footer": {"text": "Discord Bot 系统通知", "iconURL": "{{bot.avatarURL}}"}, "timestamp": true}}, "variables": {"level": {"type": "string", "required": true, "description": "通知级别：info, warning, error, success", "validation": {"enum": ["info", "warning", "error", "success"]}, "defaultValue": "info"}, "title": {"type": "string", "required": true, "description": "通知标题", "defaultValue": "系统通知"}, "message": {"type": "string", "required": true, "description": "通知消息内容", "defaultValue": "这是一条系统通知消息。"}, "timestamp": {"type": "string", "required": false, "description": "事件发生时间", "defaultValue": "{{now}}"}, "source": {"type": "string", "required": false, "description": "通知来源"}, "details": {"type": "string", "required": false, "description": "详细信息或错误堆栈"}, "bot": {"type": "object", "required": false, "description": "Bot信息对象"}}, "metadata": {"createdAt": "2024-12-24T10:00:00Z", "updatedAt": "2024-12-24T10:00:00Z", "createdBy": "system", "version": "1.0.0", "usageCount": 0, "tags": ["system", "notification", "admin", "status"], "isSystem": true}}