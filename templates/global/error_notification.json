{"templateId": "error_notification", "name": "系统错误通知", "description": "用于通知系统错误和异常情况", "type": "EMBED", "category": "system", "version": "1.0.0", "isActive": true, "guildId": null, "accessLevel": "GLOBAL", "content": {"embed": {"title": "⚠️ 系统错误警报", "description": "{{#if error_description}}{{error_description}}{{else}}系统发生了一个错误，需要立即关注。{{/if}}", "color": "#ff0000", "fields": [{"name": "🔍 错误详情", "value": "**错误类型**: {{error_type}}\n**错误消息**: {{error_message}}\n**错误代码**: {{#if error_code}}{{error_code}}{{else}}未知{{/if}}", "inline": false}, {"name": "📍 错误位置", "value": "**服务器**: {{#if guild}}{{guild.name}} ({{guild.id}}){{else}}全局{{/if}}\n**模块**: {{#if module}}{{module}}{{else}}未知模块{{/if}}\n**函数**: {{#if function}}{{function}}{{else}}未知函数{{/if}}", "inline": true}, {"name": "⏰ 时间信息", "value": "**发生时间**: {{formatDate timestamp 'YYYY-MM-DD HH:mm:ss'}}\n**错误级别**: {{upperCase error_level}}", "inline": true}], "footer": {"text": "自动错误报告系统 | 请立即检查日志以获取更多详细信息"}, "timestamp": true}}, "variables": {"error_type": {"type": "string", "required": true, "description": "错误类型（如：数据库错误、网络错误等）", "defaultValue": "未知错误"}, "error_message": {"type": "string", "required": true, "description": "详细的错误消息", "defaultValue": "没有提供错误消息"}, "error_code": {"type": "string", "required": false, "description": "错误代码或标识符"}, "error_description": {"type": "string", "required": false, "description": "用户友好的错误描述"}, "error_level": {"type": "string", "required": false, "description": "错误级别（如：error、warning、critical）", "defaultValue": "error"}, "timestamp": {"type": "string", "required": false, "description": "错误发生的时间戳"}, "guild": {"type": "object", "required": false, "description": "发生错误的Discord服务器对象"}, "module": {"type": "string", "required": false, "description": "发生错误的模块名称"}, "function": {"type": "string", "required": false, "description": "发生错误的函数名称"}}, "metadata": {"createdAt": "2024-12-24T00:00:00.000Z", "updatedAt": "2024-12-24T00:00:00.000Z", "createdBy": "system", "author": "通知系统", "version": "1.0.0", "usageCount": 0, "tags": ["error", "system", "alert", "monitoring"], "isSystem": true}}