{
  "templateId": "amazon_monitor",
  "name": "Amazon商品监控",
  "description": "用于监控Amazon商品价格、库存和状态变化",
  "type": "EMBED",
  "category": "monitors",
  "version": "1.0.0",
  "isActive": true,
  "guildId": null,
  "accessLevel": "USER",
  "content": {
    "embed": {
      "title": "📦 {{title}}",
      "url": "{{url}}",
      "color": "{{color}}",
      "timestamp": "{{timestamp}}",
      "thumbnail": {
        "url": "{{imageUrl}}"
      },
      "fields": [
        {
          "name": "💰 PRICE",
          "value": "{{price}} {{currency}}",
          "inline": true
        },
        {
          "name": "🆔 ASIN",
          "value": "`{{productId}}`",
          "inline": true
        },
        {
          "name": "📊 STOCK",
          "value": "{{availability}}",
          "inline": true
        },
        {
          "name": "🛒 ATC",
          "value": "{{atcLink}}",
          "inline": true
        }
      ],
      "footer": {
        "text": "🌐 POPNEST • {{name}}"
      }
    }
  },
  "variables": {
    "title": {
      "type": "string",
      "required": true,
      "description": "商品标题",
      "defaultValue": ""
    },
    "url": {
      "type": "string",
      "required": true,
      "description": "商品链接",
      "defaultValue": ""
    },
    "timestamp": {
      "type": "string",
      "required": false,
      "description": "时间戳",
      "defaultValue": "{{now}}"
    },
    "imageUrl": {
      "type": "string",
      "required": false,
      "description": "商品图片URL",
      "defaultValue": ""
    },
    "color": {
      "type": "string",
      "required": false,
      "description": "嵌入消息颜色 (有库存: 0x00dd00, 无库存: 0xff4444)",
      "defaultValue": "0x00dd00"
    },
    "atcLink": {
      "type": "string",
      "required": false,
      "description": "添加到购物车链接",
      "defaultValue": ""
    },
    "name": {
      "type": "string",
      "required": false,
      "description": "监控器名称",
      "defaultValue": "Amazon Monitor"
    },
    "currency": {
      "type": "string",
      "required": true,
      "description": "货币符号",
      "defaultValue": ""
    },
    "price": {
      "type": "string",
      "required": true,
      "description": "商品价格",
      "defaultValue": ""
    },
    "productId": {
      "type": "string",
      "required": true,
      "description": "Amazon ASIN",
      "defaultValue": ""
    },
    "availability": {
      "type": "string",
      "required": true,
      "description": "库存状态",
      "defaultValue": ""
    },

  },
  "tags": ["monitor", "amazon", "ecommerce", "price-tracking"],
  "usage": {
    "description": "用于监控Amazon商品的价格和库存变化",
    "examples": [
      {
        "title": "基本使用",
        "variables": {
          "title": "iPhone 15 Pro Max",
          "price": "$1199.00",
          "productId": "B0CHX1W5Y2",
          "availability": "In Stock",
          "country": "Amazon US"
        }
      }
    ]
  }
} 