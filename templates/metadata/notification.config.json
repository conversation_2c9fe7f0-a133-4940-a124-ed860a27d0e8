{"templateManager": {"templatesPath": "src/config/templates", "cacheEnabled": true, "cacheTTL": 300000, "watchFiles": true, "validateOnLoad": true}, "messageSender": {"retryCount": 3, "retryDelay": 1000, "timeout": 30000, "rateLimit": {"enabled": true, "maxRequests": 50, "timeWindow": 60000}}, "historySize": 1000, "enableMetrics": true, "handlebarsOptions": {"strict": true, "noEscape": false, "preventIndent": false}, "defaultVariables": {"bot": {"name": "<PERSON><PERSON>", "version": "1.0.0"}}}