# 服务注册调试指南

## 修复总结

### 1. 接口不匹配问题已修复
- **问题**：`FieldMapper.MapFields` 接口期望返回 `(*ProductItem, error)`
- **实际**：`FieldMappingService.MapFields` 返回 `(*MappingResult, error)`
- **修复**：更新接口定义匹配实际实现

### 2. 添加了详细调试日志
- 服务管理器状态检查
- 服务获取和类型断言过程
- 命令创建和注册状态

## 预期日志输出

### 正常情况下应该看到：
```
{"level":"info","message":"开始注册默认命令"}
{"level":"info","message":"基础命令注册完成","count":2}
{"level":"info","message":"服务管理器可用，开始注册服务依赖命令"}
{"level":"info","message":"ForwardRuleService 类型断言成功"}
{"level":"info","message":"FieldMappingService 类型断言成功"}
{"level":"info","message":"ForwardCommand 创建成功"}
{"level":"info","message":"注册命令","name":"forward","description":"管理转发规则：添加、删除、列出、查询转发规则"}
{"level":"info","message":"服务依赖命令注册完成","service_commands_count":1}
{"level":"info","message":"默认命令注册完成","total_commands":3}
```

### 如果服务不可用会看到：
```
{"level":"error","message":"获取 ForwardRuleService 失败","error":"服务 ForwardRuleService 不存在"}
{"level":"error","message":"获取 FieldMappingService 失败","error":"服务 FieldMappingService 不存在"}
{"level":"warn","message":"ForwardCommand 创建跳过","forwardManager_nil":true,"fieldMapper_nil":true}
```

### 如果类型断言失败会看到：
```
{"level":"error","message":"ForwardRuleService 类型断言失败","service_type":"*forward.ForwardRuleService"}
{"level":"error","message":"FieldMappingService 类型断言失败","service_type":"*mapping.FieldMappingService"}
```

## 验证步骤

1. **重新启动应用程序**
2. **检查启动日志**，确认看到上述正常情况的日志
3. **测试 forward 命令**：
   ```
   /forward add source_channel:#channel1 target_channel:#channel2
   ```
4. **验证不再出现 "未知命令: forward" 错误**

## 可能的问题和解决方案

### 问题1：服务未注册
- **症状**：看到 "获取 ForwardRuleService 失败" 错误
- **解决**：检查 `bot.go` 中的 `registerServices()` 方法

### 问题2：服务未启动
- **症状**：服务获取成功但类型断言失败
- **解决**：检查服务启动顺序和依赖关系

### 问题3：配置文件问题
- **症状**：服务启动失败
- **解决**：检查 `configs/forward_rules.yaml` 和 `configs/field_mapping_groups.yaml` 文件

## 下一步

如果命令注册成功但执行时仍有问题，需要检查：
1. 服务的运行状态
2. 配置文件的有效性
3. 权限设置
4. Discord 命令同步状态
