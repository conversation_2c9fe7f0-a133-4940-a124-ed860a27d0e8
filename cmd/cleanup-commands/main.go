package main

import (
	"fmt"
	"log"

	"zeka-go/internal/config"
	"zeka-go/internal/handlers"

	"github.com/bwmarrin/discordgo"
)

func main() {
	fmt.Println("🧹 Discord 命令清理工具")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建 Discord 会话
	session, err := discordgo.New("Bot " + cfg.Discord.Token)
	if err != nil {
		log.Fatalf("创建 Discord 会话失败: %v", err)
	}

	// 创建命令管理器
	commandManager := handlers.NewCommandManager(session, cfg)

	// 连接到 Discord
	if err := session.Open(); err != nil {
		log.Fatalf("连接 Discord 失败: %v", err)
	}
	defer session.Close()

	fmt.Println("🔗 已连接到 Discord")

	// 清理应用命令
	fmt.Println("🗑️  开始清理应用命令...")
	if err := commandManager.CleanupApplicationCommands(); err != nil {
		log.Fatalf("清理应用命令失败: %v", err)
	}

	// 也清理特定服务器的命令
	fmt.Println("🗑️  清理特定服务器命令...")
	cfg.Discord.GuildID = "1356291290890768547" // PopNest 服务器
	commandManager2 := handlers.NewCommandManager(session, cfg)
	if err := commandManager2.CleanupApplicationCommands(); err != nil {
		fmt.Printf("⚠️  清理服务器命令失败: %v\n", err)
	}

	fmt.Println("✅ 应用命令清理完成")
	fmt.Println("💡 现在可以重新启动 Bot 来注册新命令")
}
